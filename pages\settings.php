<?php
/**
 * Settings Page - Clean Version
 * MyGym Management System
 */

session_start();
require_once '../config/database.php';
require_once '../includes/auth.php';

// Require admin access
Auth::requireAdmin();

// Load required classes
require_once '../includes/Config.php';
require_once '../includes/ActivityLogger.php';
require_once '../includes/DatabaseSetup.php';

// Ensure settings table exists
DatabaseSetup::ensureTablesExist();

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    
    // Verify CSRF token
    if (!Auth::verifyCsrfToken($_POST['csrf_token'] ?? '')) {
        $error = 'Security token verification failed. Please try again.';
    } else {
        try {
            $db = Database::getInstance();
            $savedCount = 0;

            // Simple approach: Save all submitted fields that have values
            $settingsToSave = [
                'gym_name' => trim($_POST['gym_name'] ?? ''),
                'gym_email' => trim($_POST['gym_email'] ?? ''),
                'gym_phone' => trim($_POST['gym_phone'] ?? ''),
                'gym_address' => trim($_POST['gym_address'] ?? ''),
                'currency' => $_POST['currency'] ?? 'USD',
                'language' => $_POST['language'] ?? 'en',
                'smtp_host' => trim($_POST['smtp_host'] ?? ''),
                'smtp_port' => (int)($_POST['smtp_port'] ?? 587),
                'smtp_user' => trim($_POST['smtp_user'] ?? ''),
                'smtp_pass' => trim($_POST['smtp_pass'] ?? ''),
                'smtp_encryption' => $_POST['smtp_encryption'] ?? 'tls',
                'sms_provider' => $_POST['sms_provider'] ?? 'demo',
                'sms_api_key' => trim($_POST['sms_api_key'] ?? ''),
                'sms_sender_id' => trim($_POST['sms_sender_id'] ?? ''),
                'sms_server_url' => trim($_POST['sms_server_url'] ?? ''),
                'sms_username' => trim($_POST['sms_username'] ?? '')
            ];

            // Save all settings
            foreach ($settingsToSave as $key => $value) {
                $stmt = $db->query("
                    INSERT INTO settings (`key`, `value`)
                    VALUES (?, ?)
                    ON DUPLICATE KEY UPDATE `value` = VALUES(`value`), `updated_at` = CURRENT_TIMESTAMP
                ", [$key, $value]);

                if ($stmt) {
                    $savedCount++;
                }
            }
            
            if ($savedCount > 0) {
                $message = "Settings saved successfully!";
                $messageType = 'success';

                // Log activity
                try {
                    ActivityLogger::log('Settings Updated', 'settings', null, null, $settingsToSave);
                } catch (Exception $e) {
                    // Ignore logging errors
                }
            } else {
                throw new Exception('Failed to save settings');
            }
            
        } catch (Exception $e) {
            $error = 'Error saving settings: ' . $e->getMessage();
        }
    }
}

// Get current settings
$currentSettings = [];
try {
    $db = Database::getInstance();
    $dbSettings = $db->fetchAll("SELECT `key`, `value` FROM settings");
    foreach ($dbSettings as $setting) {
        $currentSettings[$setting['key']] = $setting['value'];
    }
} catch (Exception $e) {
    $currentSettings = [];
}

// If form was submitted, keep the submitted values in the form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    // Override current settings with submitted values for display
    foreach ($_POST as $key => $value) {
        if ($key !== 'save_settings' && $key !== 'csrf_token') {
            $currentSettings[$key] = $value;
        }
    }
}

// Set defaults
$defaults = [
    'gym_name' => 'MyGym Fitness Center',
    'gym_email' => '',
    'gym_phone' => '',
    'gym_address' => '',
    'currency' => 'USD',
    'language' => 'en',
    'smtp_host' => '',
    'smtp_port' => 587,
    'smtp_user' => '',
    'smtp_pass' => '',
    'smtp_encryption' => 'tls',
    'sms_provider' => 'demo',
    'sms_api_key' => '',
    'sms_sender_id' => '',
    'sms_server_url' => '',
    'sms_username' => 'admin'
];

foreach ($defaults as $key => $default) {
    if (!isset($currentSettings[$key])) {
        $currentSettings[$key] = $default;
    }
}

$pageTitle = 'Settings';
include '../includes/header.php';
?>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">⚙️ Settings</h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">
                Configure your gym settings and preferences.
            </p>
        </div>
        
        <!-- Messages -->
        <?php if ($message): ?>
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div class="flex">
                    <i class="fas fa-check-circle text-green-400 mt-0.5 mr-3"></i>
                    <span class="text-green-800"><?= htmlspecialchars($message) ?></span>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <div class="flex">
                    <i class="fas fa-exclamation-circle text-red-400 mt-0.5 mr-3"></i>
                    <span class="text-red-800"><?= htmlspecialchars($error) ?></span>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Settings Form -->
        <form method="POST" class="space-y-8">
            <?= Auth::csrfField() ?>
            
            <!-- General Settings -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    <i class="fas fa-cog text-blue-500 mr-2"></i>General Settings
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="gym_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Gym Name
                        </label>
                        <input type="text"
                               id="gym_name"
                               name="gym_name"
                               value="<?= htmlspecialchars($currentSettings['gym_name']) ?>"
                               placeholder="Enter gym name (leave empty to keep current)"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div>
                        <label for="gym_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Contact Email
                        </label>
                        <input type="email"
                               id="gym_email"
                               name="gym_email"
                               value="<?= htmlspecialchars($currentSettings['gym_email']) ?>"
                               placeholder="Enter email (leave empty to keep current)"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div>
                        <label for="gym_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Phone Number
                        </label>
                        <input type="tel"
                               id="gym_phone"
                               name="gym_phone"
                               value="<?= htmlspecialchars($currentSettings['gym_phone']) ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div>
                        <label for="currency" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Currency
                        </label>
                        <select id="currency"
                                name="currency"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="USD" <?= $currentSettings['currency'] === 'USD' ? 'selected' : '' ?>>USD ($)</option>
                            <option value="EUR" <?= $currentSettings['currency'] === 'EUR' ? 'selected' : '' ?>>EUR (€)</option>
                            <option value="GBP" <?= $currentSettings['currency'] === 'GBP' ? 'selected' : '' ?>>GBP (£)</option>
                        </select>
                    </div>
                </div>
                
                <div class="mt-6">
                    <label for="gym_address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Address
                    </label>
                    <textarea id="gym_address"
                              name="gym_address"
                              rows="3"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"><?= htmlspecialchars($currentSettings['gym_address']) ?></textarea>
                </div>
            </div>

            <!-- Email Settings -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    <i class="fas fa-envelope text-purple-500 mr-2"></i>Email Settings
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="smtp_host" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            SMTP Host
                        </label>
                        <input type="text"
                               id="smtp_host"
                               name="smtp_host"
                               value="<?= htmlspecialchars($currentSettings['smtp_host']) ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="smtp.gmail.com">
                    </div>

                    <div>
                        <label for="smtp_port" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            SMTP Port
                        </label>
                        <input type="number"
                               id="smtp_port"
                               name="smtp_port"
                               value="<?= htmlspecialchars($currentSettings['smtp_port']) ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               min="1" max="65535">
                    </div>

                    <div>
                        <label for="smtp_user" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            SMTP Username
                        </label>
                        <input type="text"
                               id="smtp_user"
                               name="smtp_user"
                               value="<?= htmlspecialchars($currentSettings['smtp_user']) ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="<EMAIL>">
                    </div>

                    <div>
                        <label for="smtp_pass" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            SMTP Password
                        </label>
                        <input type="password"
                               id="smtp_pass"
                               name="smtp_pass"
                               value="<?= htmlspecialchars($currentSettings['smtp_pass']) ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="Enter SMTP password">
                    </div>

                    <div>
                        <label for="smtp_encryption" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Encryption
                        </label>
                        <select id="smtp_encryption"
                                name="smtp_encryption"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="tls" <?= $currentSettings['smtp_encryption'] === 'tls' ? 'selected' : '' ?>>TLS</option>
                            <option value="ssl" <?= $currentSettings['smtp_encryption'] === 'ssl' ? 'selected' : '' ?>>SSL</option>
                            <option value="none" <?= $currentSettings['smtp_encryption'] === 'none' ? 'selected' : '' ?>>None</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- SMS Settings -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    <i class="fas fa-sms text-green-500 mr-2"></i>SMS Settings
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="sms_provider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            SMS Provider
                        </label>
                        <select id="sms_provider"
                                name="sms_provider"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                onchange="toggleSmsGateFields()">
                            <option value="demo" <?= ($currentSettings['sms_provider'] ?? 'demo') === 'demo' ? 'selected' : '' ?>>Demo Mode (Testing)</option>
                            <option value="smsgate" <?= ($currentSettings['sms_provider'] ?? '') === 'smsgate' ? 'selected' : '' ?>>SMS-Gate.app (Android Device)</option>
                            <option value="twilio" <?= ($currentSettings['sms_provider'] ?? '') === 'twilio' ? 'selected' : '' ?>>Twilio</option>
                            <option value="textlocal" <?= ($currentSettings['sms_provider'] ?? '') === 'textlocal' ? 'selected' : '' ?>>TextLocal</option>
                            <option value="nexmo" <?= ($currentSettings['sms_provider'] ?? '') === 'nexmo' ? 'selected' : '' ?>>Nexmo/Vonage</option>
                            <option value="msg91" <?= ($currentSettings['sms_provider'] ?? '') === 'msg91' ? 'selected' : '' ?>>MSG91</option>
                        </select>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Choose your SMS service provider</p>
                    </div>

                    <div>
                        <label for="sms_sender_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Sender ID
                        </label>
                        <input type="text"
                               id="sms_sender_id"
                               name="sms_sender_id"
                               value="<?= htmlspecialchars($currentSettings['sms_sender_id'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="MyGym">
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Name that appears as sender (6-11 characters)</p>
                    </div>

                    <!-- SMS-Gate Server URL (only for SMS-Gate provider) -->
                    <div id="sms-gate-url-field" class="md:col-span-2" style="display: none;">
                        <label for="sms_server_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            SMS-Gate Server URL
                        </label>
                        <input type="url"
                               id="sms_server_url"
                               name="sms_server_url"
                               value="<?= htmlspecialchars($currentSettings['sms_server_url'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="http://*************:8080 or https://api.sms-gate.app">
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            <strong>Local:</strong> http://*************:8080 (your device IP)<br>
                            <strong>Cloud:</strong> https://api.sms-gate.app
                        </p>
                    </div>

                    <!-- SMS-Gate Username (only for SMS-Gate provider) -->
                    <div id="sms-gate-username-field" class="md:col-span-1" style="display: none;">
                        <label for="sms_username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Username
                        </label>
                        <input type="text"
                               id="sms_username"
                               name="sms_username"
                               value="<?= htmlspecialchars($currentSettings['sms_username'] ?? 'admin') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="admin">
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Username from SMS-Gate app</p>
                    </div>

                    <div id="sms-api-key-field" class="md:col-span-2">
                        <label for="sms_api_key" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <span id="sms-api-key-label">API Key / Token</span>
                        </label>
                        <input type="password"
                               id="sms_api_key"
                               name="sms_api_key"
                               value="<?= htmlspecialchars($currentSettings['sms_api_key'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="Enter your SMS provider API key">
                        <p id="sms-api-key-help" class="text-xs text-gray-500 dark:text-gray-400 mt-1">Get this from your SMS provider dashboard</p>
                    </div>
                </div>

                <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div class="flex items-start">
                        <div class="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center mr-3 mt-0.5">
                            <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 text-xs"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200">SMS Provider Setup</h4>
                            <div id="sms-provider-info">
                                <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">
                                    <strong>SMS-Gate.app Setup (2 modes available):</strong><br><br>

                                    <strong>📱 Local Server Mode:</strong><br>
                                    1. Install SMS Gateway for Android™ from Google Play Store<br>
                                    2. Open the app and enable "Local Server"<br>
                                    3. Note the IP address and credentials shown in the app<br>
                                    4. Enter the server URL (e.g., http://*************:8080)<br>
                                    5. Enter the username and password from the app<br><br>

                                    <strong>☁️ Cloud Server Mode:</strong><br>
                                    1. Install SMS Gateway for Android™ from Google Play Store<br>
                                    2. Open the app and enable "Cloud Server"<br>
                                    3. Connect your device to the cloud service<br>
                                    4. Enter server URL: <code>https://api.sms-gate.app</code><br>
                                    5. Enter the username and password from the app<br><br>

                                    <strong>Other providers:</strong> Sign up with an SMS service and get an API key.<br>
                                    <strong>Demo mode:</strong> Test without a real provider.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
                <button type="submit"
                        name="save_settings"
                        value="1"
                        class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    <i class="fas fa-save mr-2"></i>
                    Save Changes
                </button>
            </div>

            <!-- Help Text -->
            <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 mr-3 mt-1"></i>
                    <div class="text-sm text-blue-800 dark:text-blue-200">
                        <strong>Simple Save:</strong> Click "Save Changes" to save all current form values.
                        All fields will be saved with whatever values are currently displayed.
                    </div>
                </div>
            </div>
        </form>

        <!-- Database Backup & Restore -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700 mt-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                <i class="fas fa-database text-green-500 mr-2"></i>Database Backup & Restore
            </h3>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <button onclick="createBackup()"
                        class="flex items-center justify-center px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                    <i class="fas fa-plus mr-2"></i>Create Backup
                </button>
                <button onclick="showUploadForm()"
                        class="flex items-center justify-center px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                    <i class="fas fa-upload mr-2"></i>Upload Backup
                </button>
                <button onclick="refreshBackups()"
                        class="flex items-center justify-center px-4 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
                    <i class="fas fa-refresh mr-2"></i>Refresh List
                </button>
                <div class="flex items-center justify-center px-4 py-3 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-lg">
                    <i class="fas fa-info-circle mr-2"></i>
                    <span id="backup-count">Loading...</span>
                </div>
            </div>

            <!-- Upload Form (Hidden by default) -->
            <div id="upload-form" class="hidden mb-6 p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
                <h4 class="font-semibold text-purple-900 dark:text-purple-200 mb-3">
                    <i class="fas fa-upload mr-2"></i>Upload Backup File
                </h4>
                <div class="flex items-center gap-4">
                    <input type="file"
                           id="backup-file-input"
                           accept=".sql"
                           class="flex-1 px-3 py-2 border border-purple-300 dark:border-purple-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <button onclick="uploadBackup()"
                            class="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                        <i class="fas fa-upload mr-2"></i>Upload
                    </button>
                    <button onclick="hideUploadForm()"
                            class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-lg transition-colors">
                        Cancel
                    </button>
                </div>
                <p class="text-sm text-purple-700 dark:text-purple-300 mt-2">
                    <i class="fas fa-info-circle mr-1"></i>
                    Upload a .sql backup file to restore your database. Maximum file size: 50MB.
                </p>
            </div>

            <!-- Backup List -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 class="font-semibold text-gray-900 dark:text-white mb-4">Available Backups</h4>
                <div id="backup-list" class="space-y-2">
                    <div class="text-center py-4 text-gray-500">
                        <i class="fas fa-spinner fa-spin mr-2"></i>Loading backups...
                    </div>
                </div>
            </div>

            <!-- Warning -->
            <div class="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mr-3 mt-1"></i>
                    <div class="text-sm text-yellow-800 dark:text-yellow-200">
                        <strong>Important:</strong> Always create a backup before making major changes.
                        Restoring a backup will replace ALL current data and cannot be undone.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-md w-full mx-4 shadow-2xl">
        <div class="text-center">
            <!-- Icon -->
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/20 mb-4">
                <i class="fas fa-trash text-red-600 dark:text-red-400 text-xl"></i>
            </div>

            <!-- Title -->
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Delete Backup
            </h3>

            <!-- Message -->
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
                Are you sure you want to delete "<span id="deleteFileName" class="font-medium text-gray-900 dark:text-white"></span>"?
                <br><br>
                <span class="text-red-600 dark:text-red-400 font-medium">This action cannot be undone.</span>
            </p>

            <!-- Buttons -->
            <div class="flex space-x-3">
                <button onclick="cancelDelete()"
                        class="flex-1 px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
                    <i class="fas fa-times mr-2"></i>Cancel
                </button>
                <button onclick="executeDelete()"
                        class="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                    <i class="fas fa-trash mr-2"></i>Delete
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Simple Backup System JavaScript
document.addEventListener('DOMContentLoaded', function() {
    refreshBackups();
});

// Create new backup
function createBackup() {
    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating...';

    const formData = new FormData();
    formData.append('action', 'create');
    formData.append('description', 'Manual backup from settings');

    fetch('../api/simple-backup.php', {
        method: 'POST',
        body: formData,
        timeout: 30000 // 30 second timeout
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.text();
    })
    .then(text => {
        if (!text || text.trim() === '') {
            throw new Error('Empty response from server');
        }
        try {
            return JSON.parse(text);
        } catch (e) {
            console.error('Invalid JSON response:', text);
            throw new Error('Server returned invalid response: ' + text.substring(0, 100));
        }
    })
    .then(data => {
        if (data.success) {
            showMessage('Backup created successfully!', 'success');
            refreshBackups();
        } else {
            showMessage('Backup failed: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showMessage('Network error: ' + error.message, 'error');
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

// Show upload form
function showUploadForm() {
    document.getElementById('upload-form').classList.remove('hidden');
}

// Hide upload form
function hideUploadForm() {
    document.getElementById('upload-form').classList.add('hidden');
    document.getElementById('backup-file-input').value = '';
}

// Upload backup file
function uploadBackup() {
    const fileInput = document.getElementById('backup-file-input');
    const file = fileInput.files[0];

    if (!file) {
        showMessage('Please select a backup file', 'error');
        return;
    }

    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Uploading...';

    const formData = new FormData();
    formData.append('action', 'upload');
    formData.append('backup_file', file);

    fetch('../api/simple-backup.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('Backup uploaded successfully!', 'success');
            hideUploadForm();
            refreshBackups();
        } else {
            showMessage('Upload failed: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showMessage('Network error: ' + error.message, 'error');
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

// Refresh backup list
function refreshBackups() {
    const listContainer = document.getElementById('backup-list');
    const countContainer = document.getElementById('backup-count');

    listContainer.innerHTML = '<div class="text-center py-4 text-gray-500"><i class="fas fa-spinner fa-spin mr-2"></i>Loading...</div>';
    countContainer.textContent = 'Loading...';

    fetch('../api/simple-backup.php?action=list')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderBackupList(data.backups);
            countContainer.textContent = `${data.info.count} backups (${data.info.total_size})`;
        } else {
            listContainer.innerHTML = '<div class="text-center py-4 text-red-500">Failed to load backups</div>';
            countContainer.textContent = 'Error';
        }
    })
    .catch(error => {
        listContainer.innerHTML = '<div class="text-center py-4 text-red-500">Network error</div>';
        countContainer.textContent = 'Error';
    });
}

// Render backup list
function renderBackupList(backups) {
    const listContainer = document.getElementById('backup-list');

    if (backups.length === 0) {
        listContainer.innerHTML = '<div class="text-center py-8 text-gray-500">No backups found. Create your first backup!</div>';
        return;
    }

    let html = '';
    backups.forEach(backup => {
        html += `
            <div class="flex items-center justify-between p-3 bg-white dark:bg-gray-600 rounded-lg border">
                <div class="flex-1">
                    <div class="font-medium text-gray-900 dark:text-white">${backup.filename}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">${backup.date} • ${backup.size}</div>
                </div>
                <div class="flex items-center space-x-2">
                    <button onclick="downloadBackup('${backup.filename}')"
                            class="px-3 py-1 text-sm bg-blue-100 hover:bg-blue-200 text-blue-700 rounded transition-colors"
                            title="Download">
                        <i class="fas fa-download"></i>
                    </button>
                    <button onclick="confirmRestore('${backup.filename}')"
                            class="px-3 py-1 text-sm bg-green-100 hover:bg-green-200 text-green-700 rounded transition-colors"
                            title="Restore">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button onclick="showDeleteModal('${backup.filename}')"
                            class="px-3 py-1 text-sm bg-red-100 hover:bg-red-200 text-red-700 rounded transition-colors"
                            title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    });

    listContainer.innerHTML = html;
}

// Download backup
function downloadBackup(filename) {
    window.open(`../api/simple-backup.php?action=download&filename=${encodeURIComponent(filename)}`, '_blank');
}

// Confirm restore
function confirmRestore(filename) {
    const message = `⚠️ CRITICAL WARNING ⚠️

This will restore the database from "${filename}" and COMPLETELY REPLACE ALL CURRENT DATA including:

• All member information
• Payment records
• Settings and configurations
• All other data in the system

This action CANNOT be undone!

Are you absolutely sure you want to proceed?

Click OK only if you want to restore from this backup.
Click Cancel to keep your current data.`;

    if (confirm(message)) {
        restoreBackup(filename);
    }
}

// Restore backup
function restoreBackup(filename) {
    const formData = new FormData();
    formData.append('action', 'restore');
    formData.append('filename', filename);

    showMessage('Restoring database... Please wait.', 'info');

    fetch('../api/simple-backup.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('Database restored successfully! Page will reload.', 'success');
            setTimeout(() => window.location.reload(), 2000);
        } else {
            showMessage('Restore failed: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showMessage('Network error: ' + error.message, 'error');
    });
}

// Global variable to store filename for deletion
let fileToDelete = null;

// Show custom delete modal
function showDeleteModal(filename) {
    console.log('showDeleteModal called for:', filename);
    fileToDelete = filename;

    // Set the filename in the modal
    document.getElementById('deleteFileName').textContent = filename;

    // Show the modal
    document.getElementById('deleteModal').classList.remove('hidden');
}

// Cancel delete - hide modal
function cancelDelete() {
    console.log('Delete cancelled by user');
    fileToDelete = null;
    document.getElementById('deleteModal').classList.add('hidden');
}

// Execute delete - call the actual delete function
function executeDelete() {
    console.log('Delete confirmed by user for:', fileToDelete);

    if (fileToDelete) {
        // Hide modal first
        document.getElementById('deleteModal').classList.add('hidden');

        // Call the delete function
        deleteBackup(fileToDelete);

        // Reset the variable
        fileToDelete = null;
    }
}

// Confirm delete
function confirmDelete(filename) {
    console.log('confirmDelete called for:', filename);

    const confirmed = confirm(`Are you sure you want to delete "${filename}"?\n\nThis action cannot be undone.`);
    console.log('User confirmed deletion:', confirmed);

    if (confirmed) {
        console.log('Calling deleteBackup function...');
        deleteBackup(filename);
    } else {
        console.log('User cancelled deletion');
    }
}

// Delete backup
function deleteBackup(filename) {
    console.log('Delete function called for:', filename);

    const formData = new FormData();
    formData.append('action', 'delete');
    formData.append('filename', filename);

    console.log('Sending delete request...');

    fetch('../api/simple-backup.php', {
        method: 'POST',
        body: formData,
        timeout: 30000
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.text();
    })
    .then(text => {
        if (!text || text.trim() === '') {
            throw new Error('Empty response from server');
        }
        try {
            return JSON.parse(text);
        } catch (e) {
            console.error('Invalid JSON response:', text);
            throw new Error('Server returned invalid response: ' + text.substring(0, 100));
        }
    })
    .then(data => {
        console.log('Delete response:', data);
        if (data.success) {
            showMessage('Backup deleted successfully', 'success');
            refreshBackups();
        } else {
            showMessage('Delete failed: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        showMessage('Network error: ' + error.message, 'error');
    });
}

// Show message
function showMessage(message, type = 'info') {
    const colors = {
        success: 'bg-green-100 border-green-200 text-green-800',
        error: 'bg-red-100 border-red-200 text-red-800',
        info: 'bg-blue-100 border-blue-200 text-blue-800'
    };

    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg border ${colors[type]} z-50 max-w-md`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-lg">&times;</button>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Debug: Check if functions are defined
console.log('Functions check:');
console.log('confirmDelete defined:', typeof confirmDelete);
console.log('deleteBackup defined:', typeof deleteBackup);
console.log('createBackup defined:', typeof createBackup);
console.log('refreshBackups defined:', typeof refreshBackups);

// Test function availability
window.testDeleteFunction = function() {
    console.log('Testing delete function...');
    if (typeof confirmDelete === 'function') {
        confirmDelete('test-file.sql');
    } else {
        console.error('confirmDelete function not found!');
        alert('confirmDelete function not found!');
    }
};

// SMS-Gate provider field toggle
function toggleSmsGateFields() {
    const provider = document.getElementById('sms_provider').value;
    const urlField = document.getElementById('sms-gate-url-field');
    const usernameField = document.getElementById('sms-gate-username-field');
    const apiKeyField = document.getElementById('sms-api-key-field');
    const apiKeyLabel = document.getElementById('sms-api-key-label');
    const apiKeyHelp = document.getElementById('sms-api-key-help');
    const apiKeyInput = document.getElementById('sms_api_key');

    if (provider === 'smsgate') {
        // Show SMS-Gate specific fields
        urlField.style.display = 'block';
        usernameField.style.display = 'block';
        apiKeyField.className = 'md:col-span-1'; // Make API key field single column
        apiKeyLabel.textContent = 'Password';
        apiKeyHelp.textContent = 'Password from SMS-Gate app (shown in app credentials)';
        apiKeyInput.placeholder = 'Enter password from SMS-Gate app';
        apiKeyInput.required = false;
    } else {
        // Hide SMS-Gate specific fields
        urlField.style.display = 'none';
        usernameField.style.display = 'none';
        apiKeyField.className = 'md:col-span-2'; // Make API key field full width
        apiKeyLabel.textContent = 'API Key / Token';
        apiKeyHelp.textContent = 'Get this from your SMS provider dashboard';
        apiKeyInput.placeholder = 'Enter your SMS provider API key';
        apiKeyInput.required = provider !== 'demo';
    }
}

// Initialize SMS-Gate fields on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleSmsGateFields();
});

console.log('Settings page JavaScript loaded successfully');
</script>

<?php include '../includes/footer.php'; ?>
