<?php
/**
 * Database Backup & Restore API
 * MyGym Management System
 */

session_start();

// Include required files
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/ActivityLogger.php';

// Check authentication and admin access
if (!isset($_SESSION['user_id']) && !isset($_SESSION['local_admin_username'])) {
    http_response_code(401);
    exit('Unauthorized');
}

// Require admin access for database operations
Auth::requireAdmin();

$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    $db = Database::getInstance();
    
    switch ($action) {
        case 'backup':
            createBackup();
            break;
        case 'restore':
            restoreBackup();
            break;
        case 'list':
            listBackups();
            break;
        case 'delete':
            deleteBackup();
            break;
        default:
            http_response_code(400);
            exit('Invalid action');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    exit('Database operation error: ' . $e->getMessage());
}

function createBackup() {
    try {
        // Create backups directory if it doesn't exist
        $backupDir = '../backups';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        // Generate backup filename with timestamp
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "mygym_backup_{$timestamp}.sql";
        $filepath = $backupDir . '/' . $filename;
        
        // Get database configuration
        $config = require '../config/config.php';
        $host = $config['database']['host'];
        $dbname = $config['database']['name'];
        $username = $config['database']['username'];
        $password = $config['database']['password'];
        
        // Create mysqldump command
        $command = sprintf(
            'mysqldump --host=%s --user=%s --password=%s --single-transaction --routines --triggers %s > %s',
            escapeshellarg($host),
            escapeshellarg($username),
            escapeshellarg($password),
            escapeshellarg($dbname),
            escapeshellarg($filepath)
        );
        
        // Execute backup command
        $output = [];
        $returnCode = 0;
        exec($command . ' 2>&1', $output, $returnCode);
        
        if ($returnCode !== 0) {
            // Fallback to PHP-based backup if mysqldump fails
            createPHPBackup($filepath);
        }
        
        // Verify backup file was created and has content
        if (!file_exists($filepath) || filesize($filepath) < 100) {
            throw new Exception('Backup file was not created or is empty');
        }
        
        // Log the backup activity (if logger exists)
        if (class_exists('ActivityLogger')) {
            ActivityLogger::log('Database Backup Created', 'system', null, null, [
                'filename' => $filename,
                'size' => filesize($filepath)
            ]);
        }
        
        // Return success response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Database backup created successfully',
            'filename' => $filename,
            'size' => formatBytes(filesize($filepath)),
            'created' => date('F j, Y g:i A')
        ]);
        
    } catch (Exception $e) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Backup failed: ' . $e->getMessage()
        ]);
    }
}

function createPHPBackup($filepath) {
    $db = Database::getInstance();
    
    // Get all tables
    $tables = $db->fetchAll("SHOW TABLES");
    $config = require '../config/config.php';
    $tableColumn = 'Tables_in_' . $config['database']['name'];
    
    $sql = "-- MyGym Database Backup\n";
    $sql .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n\n";
    $sql .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";
    
    foreach ($tables as $table) {
        $tableName = $table[$tableColumn];
        
        // Get table structure
        $createTable = $db->fetch("SHOW CREATE TABLE `{$tableName}`");
        $sql .= "-- Table structure for `{$tableName}`\n";
        $sql .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
        $sql .= $createTable['Create Table'] . ";\n\n";
        
        // Get table data
        $rows = $db->fetchAll("SELECT * FROM `{$tableName}`");
        if (!empty($rows)) {
            $sql .= "-- Data for table `{$tableName}`\n";
            $sql .= "INSERT INTO `{$tableName}` VALUES\n";
            
            $values = [];
            foreach ($rows as $row) {
                $escapedValues = array_map(function($value) {
                    return $value === null ? 'NULL' : "'" . addslashes($value) . "'";
                }, array_values($row));
                $values[] = '(' . implode(', ', $escapedValues) . ')';
            }
            
            $sql .= implode(",\n", $values) . ";\n\n";
        }
    }
    
    $sql .= "SET FOREIGN_KEY_CHECKS = 1;\n";
    
    // Write to file
    if (file_put_contents($filepath, $sql) === false) {
        throw new Exception('Failed to write backup file');
    }
}

function restoreBackup() {
    try {
        if (!isset($_FILES['backup_file']) || $_FILES['backup_file']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('No backup file uploaded or upload error');
        }
        
        $uploadedFile = $_FILES['backup_file'];
        $filename = $uploadedFile['name'];
        $tmpPath = $uploadedFile['tmp_name'];
        
        // Validate file extension
        if (!preg_match('/\.sql$/i', $filename)) {
            throw new Exception('Invalid file type. Only .sql files are allowed');
        }
        
        // Read and validate SQL content
        $sqlContent = file_get_contents($tmpPath);
        if (empty($sqlContent)) {
            throw new Exception('Backup file is empty');
        }
        
        // Basic validation for SQL content
        if (!preg_match('/CREATE TABLE|INSERT INTO/i', $sqlContent)) {
            throw new Exception('Invalid backup file format');
        }
        
        $db = Database::getInstance();
        
        // Disable foreign key checks
        $db->query("SET FOREIGN_KEY_CHECKS = 0");
        
        // Split SQL into individual statements
        $statements = preg_split('/;\s*$/m', $sqlContent);
        
        $executedStatements = 0;
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                try {
                    $db->query($statement);
                    $executedStatements++;
                } catch (Exception $e) {
                    // Log error but continue with other statements
                    error_log("SQL Statement Error: " . $e->getMessage());
                }
            }
        }
        
        // Re-enable foreign key checks
        $db->query("SET FOREIGN_KEY_CHECKS = 1");
        
        // Log the restore activity (if logger exists)
        if (class_exists('ActivityLogger')) {
            ActivityLogger::log('Database Restored', 'system', null, null, [
                'filename' => $filename,
                'statements_executed' => $executedStatements
            ]);
        }
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Database restored successfully',
            'statements_executed' => $executedStatements
        ]);
        
    } catch (Exception $e) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Restore failed: ' . $e->getMessage()
        ]);
    }
}

function listBackups() {
    try {
        $backupDir = '../backups';
        $backups = [];
        
        if (is_dir($backupDir)) {
            $files = glob($backupDir . '/*.sql');
            
            foreach ($files as $file) {
                $filename = basename($file);
                $backups[] = [
                    'filename' => $filename,
                    'size' => formatBytes(filesize($file)),
                    'created' => date('F j, Y g:i A', filemtime($file)),
                    'timestamp' => filemtime($file)
                ];
            }
            
            // Sort by creation time (newest first)
            usort($backups, function($a, $b) {
                return $b['timestamp'] - $a['timestamp'];
            });
        }
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'backups' => $backups
        ]);
        
    } catch (Exception $e) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to list backups: ' . $e->getMessage()
        ]);
    }
}

function deleteBackup() {
    try {
        $filename = $_POST['filename'] ?? '';
        if (empty($filename)) {
            throw new Exception('No filename provided');
        }
        
        // Validate filename to prevent directory traversal
        if (!preg_match('/^mygym_backup_[\d\-_]+\.sql$/', $filename)) {
            throw new Exception('Invalid filename format');
        }
        
        $filepath = '../backups/' . $filename;
        
        if (!file_exists($filepath)) {
            throw new Exception('Backup file not found');
        }
        
        if (!unlink($filepath)) {
            throw new Exception('Failed to delete backup file');
        }
        
        // Log the deletion (if logger exists)
        if (class_exists('ActivityLogger')) {
            ActivityLogger::log('Database Backup Deleted', 'system', null, null, [
                'filename' => $filename
            ]);
        }
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Backup deleted successfully'
        ]);
        
    } catch (Exception $e) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Delete failed: ' . $e->getMessage()
        ]);
    }
}

function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $base = log($size, 1024);
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $units[floor($base)];
}
?>
