<?php
/**
 * Notifications Management Page
 * MyGym Management System
 */

session_start();
ob_start(); // Start output buffering to prevent header issues
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/Config.php';
require_once '../includes/EmailService.php';
require_once '../includes/EmailTemplates.php';
require_once '../includes/SmsService.php';
require_once '../includes/SmsTemplates.php';

// Require authentication and permission
Auth::requireAuth();
Auth::requirePermission('notifications.send');

$db = Database::getInstance();
$action = $_GET['action'] ?? 'list';
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'send_email') {
        $result = handleSendEmail();
        if ($result['success']) {
            Session::success($result['message']);
        } else {
            Session::error($result['message']);
        }
        header('Location: notifications.php');
        exit;
    } elseif ($action === 'send_sms') {
        $result = handleSendSMS();
        if ($result['success']) {
            Session::success($result['message']);
        } else {
            Session::error($result['message']);
        }
        header('Location: notifications.php');
        exit;
    } elseif ($action === 'test_email') {
        // Handle AJAX test email request
        header('Content-Type: application/json');
        $result = handleTestEmail();
        echo json_encode($result);
        exit;
    } elseif ($action === 'test_sms') {
        // Handle AJAX test SMS request
        header('Content-Type: application/json');
        $result = handleTestSMS();
        echo json_encode($result);
        exit;
    } elseif ($action === 'debug_sms') {
        // Handle AJAX debug SMS request
        header('Content-Type: application/json');
        $result = handleDebugSMS();
        echo json_encode($result);
        exit;
    } elseif ($action === 'test_connection') {
        // Handle AJAX connection test request
        header('Content-Type: application/json');
        $result = handleTestConnection();
        echo json_encode($result);
        exit;
    } elseif ($action === 'send_expired_notifications') {
        // Handle AJAX expired membership notifications request
        header('Content-Type: application/json');
        $result = handleExpiredMembershipNotifications();
        echo json_encode($result);
        exit;
    }
}

function handleSendEmail() {
    global $db;

    $memberIds = $_POST['member_ids'] ?? [];
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');
    $emailTemplate = $_POST['email_template'] ?? '';

    // Check if email is configured
    $emailService = new EmailService();
    if (!$emailService->isConfigured()) {
        return [
            'success' => false,
            'message' => 'Email is not configured. Please configure SMTP settings in the Settings page first.'
        ];
    }
    
    if (empty($memberIds)) {
        return ['success' => false, 'message' => 'Please select at least one member.'];
    }
    
    if (empty($subject) || empty($message)) {
        return ['success' => false, 'message' => 'Subject and message are required.'];
    }
    
    try {

        $sentCount = 0;
        $failedCount = 0;
        $errors = [];

        foreach ($memberIds as $memberId) {
            $member = $db->fetch("SELECT id, first_name, last_name, email FROM members WHERE id = ? AND email IS NOT NULL AND email != ''", [$memberId]);
            if ($member) {
                // Combine first and last name
                $memberName = trim($member['first_name'] . ' ' . $member['last_name']);

                // Check if using a template or custom message
                if (!empty($emailTemplate) && $emailTemplate !== 'custom') {
                    // Use templated email
                    $templateData = [
                        'member_name' => $memberName,
                        'member_id' => $member['id'],
                        'member_email' => $member['email']
                    ];

                    // Add payment-specific data for payment reminder template
                    if ($emailTemplate === 'payment_reminder') {
                        // Get member's plan information for payment amount
                        $memberPlan = $db->fetch("
                            SELECT p.name, p.price, p.duration_months
                            FROM plans p
                            JOIN members m ON m.plan_id = p.id
                            WHERE m.id = ?
                        ", [$member['id']]);

                        if ($memberPlan) {
                            $templateData['amount'] = number_format($memberPlan['price'], 2);
                            $templateData['plan_name'] = $memberPlan['name'];
                            $templateData['duration'] = $memberPlan['duration_months'];
                        } else {
                            $templateData['amount'] = '0.00';
                            $templateData['plan_name'] = 'Current Plan';
                            $templateData['duration'] = '1';
                        }

                        // Calculate due date (end of current membership)
                        if ($member['end_date']) {
                            $templateData['due_date'] = date('F j, Y', strtotime($member['end_date']));
                        } else {
                            $templateData['due_date'] = 'soon';
                        }
                    }

                    $result = $emailService->sendTemplatedEmail(
                        $member['email'],
                        $memberName,
                        $emailTemplate,
                        $templateData
                    );
                } else {
                    // Use custom message
                    $result = $emailService->sendEmail(
                        $member['email'],
                        $memberName,
                        $subject,
                        $message,
                        true // Send as HTML
                    );
                }

                if ($result['success']) {
                    $sentCount++;

                    // Log successful email send
                    if (class_exists('ActivityLogger')) {
                        ActivityLogger::log('Email Notification Sent', 'members', $memberId, null, [
                            'subject' => $subject,
                            'message' => substr($message, 0, 100) . '...',
                            'email' => $member['email'],
                            'status' => 'sent'
                        ]);
                    }
                } else {
                    $failedCount++;
                    $errors[] = "Failed to send to {$memberName} ({$member['email']}): " . $result['message'];

                    // Log failed email send
                    if (class_exists('ActivityLogger')) {
                        ActivityLogger::log('Email Notification Failed', 'members', $memberId, null, [
                            'subject' => $subject,
                            'email' => $member['email'],
                            'error' => $result['message'],
                            'status' => 'failed'
                        ]);
                    }
                }
            } else {
                $failedCount++;
                $errors[] = "Member ID {$memberId} not found or has no email address";
            }
        }

        // Create appropriate message based on results
        if ($sentCount === 0 && $failedCount > 0) {
            // All emails failed
            $responseMessage = "Failed to send email notifications to {$failedCount} member(s). ";

            // Check if it's likely a configuration issue
            $smtp_host = Config::get('smtp_host');
            if (empty($smtp_host)) {
                $responseMessage .= "Email is not configured properly. Please configure SMTP settings in the Settings page.";
            } else {
                $responseMessage .= "Please check your email configuration and try again.";
            }

            return [
                'success' => false,
                'message' => $responseMessage,
                'sent_count' => $sentCount,
                'failed_count' => $failedCount,
                'errors' => $errors
            ];
        } else if ($sentCount > 0 && $failedCount === 0) {
            // All emails sent successfully
            $responseMessage = "Email notifications sent successfully to {$sentCount} member(s).";
            return [
                'success' => true,
                'message' => $responseMessage,
                'sent_count' => $sentCount,
                'failed_count' => $failedCount,
                'errors' => $errors
            ];
        } else {
            // Mixed results
            $responseMessage = "Email notifications sent to {$sentCount} member(s). {$failedCount} emails failed to send.";
            return [
                'success' => $sentCount > 0,
                'message' => $responseMessage,
                'sent_count' => $sentCount,
                'failed_count' => $failedCount,
                'errors' => $errors
            ];
        }

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to send emails: ' . $e->getMessage()];
    }
}

function handleSendSMS() {
    global $db;

    $memberIds = $_POST['member_ids'] ?? [];
    $message = trim($_POST['sms_message'] ?? '');
    $templateType = $_POST['sms_template'] ?? 'custom';

    // Debug: Log what we received
    error_log("SMS Send Debug - Member IDs: " . print_r($memberIds, true));
    error_log("SMS Send Debug - Message: " . $message);
    error_log("SMS Send Debug - Template: " . $templateType);

    if (empty($memberIds)) {
        return ['success' => false, 'message' => 'Please select at least one member.'];
    }

    if (empty($message) && $templateType === 'custom') {
        return ['success' => false, 'message' => 'SMS message is required.'];
    }

    try {
        // Initialize SMS service
        $smsService = new SmsService();

        if (!$smsService->isConfigured()) {
            return ['success' => false, 'message' => 'SMS not configured. Please configure SMS settings first.'];
        }

        $sentCount = 0;
        $failedCount = 0;
        $results = [];

        foreach ($memberIds as $memberId) {
            $member = $db->fetch("SELECT * FROM members WHERE id = ? AND phone IS NOT NULL AND phone != ''", [$memberId]);

            // Debug: Log member lookup
            error_log("SMS Debug - Looking for member ID: " . $memberId);
            if ($member) {
                error_log("SMS Debug - Found member: " . $member['first_name'] . ' ' . $member['last_name'] . " Phone: " . $member['phone']);
                $memberName = $member['first_name'] . ' ' . $member['last_name'];
            } else {
                error_log("SMS Debug - Member not found or no phone number for ID: " . $memberId);
            }

            if ($member) {

                // Prepare template data
                $templateData = [
                    'member_name' => $memberName,
                    'custom_message' => $message
                ];

                // Send SMS using template or custom message
                if ($templateType !== 'custom') {
                    $result = $smsService->sendTemplatedSms($member['phone'], $templateType, $templateData);
                } else {
                    // Personalize custom message
                    $personalizedMessage = str_replace('{member_name}', $memberName, $message);
                    $personalizedMessage = str_replace('{gym_name}', Config::get('gym_name') ?: 'MyGym', $personalizedMessage);
                    $result = $smsService->sendSms($member['phone'], $personalizedMessage);
                }

                if ($result['success']) {
                    $sentCount++;
                    ActivityLogger::log('SMS Notification Sent', 'members', $memberId, null, [
                        'template' => $templateType,
                        'message' => substr($message, 0, 100),
                        'phone' => $member['phone']
                    ]);
                } else {
                    $failedCount++;
                    // Log the failure for debugging
                    error_log("SMS Failed for {$memberName} ({$member['phone']}): " . $result['message']);
                }

                $results[] = [
                    'member' => $memberName,
                    'phone' => $member['phone'],
                    'success' => $result['success'],
                    'message' => $result['message']
                ];
            } else {
                $failedCount++;
            }
        }

        $responseMessage = "SMS notifications sent successfully to {$sentCount} members.";
        if ($failedCount > 0) {
            $responseMessage .= " {$failedCount} members skipped (no phone number or failed to send).";

            // Add detailed error information
            $errorDetails = [];
            foreach ($results as $result) {
                if (!$result['success']) {
                    $errorDetails[] = "{$result['member']}: {$result['message']}";
                }
            }
            if (!empty($errorDetails)) {
                $responseMessage .= " Errors: " . implode('; ', $errorDetails);
            }
        }

        return ['success' => $sentCount > 0, 'message' => $responseMessage, 'results' => $results];

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to send SMS: ' . $e->getMessage()];
    }
}

function handleTestEmail() {
    try {
        // Initialize email service
        $emailService = new EmailService();

        if (!$emailService->isConfigured()) {
            return ['success' => false, 'message' => 'Email settings not configured. Please configure email settings in the Settings page first.'];
        }

        // Test email configuration
        $testResult = $emailService->testConnection();

        if (!$testResult['success']) {
            return ['success' => false, 'message' => $testResult['message']];
        }

        // Try to send a test email to the configured from email
        $config = $emailService->getConfigSummary();
        $testEmail = $config['from_email'];

        if (empty($testEmail)) {
            return ['success' => false, 'message' => 'No email address configured for testing'];
        }

        $subject = 'MyGym Email Test - ' . date('Y-m-d H:i:s');
        $message = "This is a test email from your MyGym Management System.\n\n";
        $message .= "If you received this email, your email configuration is working correctly!\n\n";
        $message .= "Test Details:\n";
        $message .= "- Sent at: " . date('Y-m-d H:i:s') . "\n";
        $message .= "- From: " . $config['from_name'] . " <" . $config['from_email'] . ">\n";
        $message .= "- System: MyGym Management System\n\n";
        $message .= "You can now send email notifications to your members.";

        $result = $emailService->sendEmail(
            $testEmail,
            'Test Recipient',
            $subject,
            $message,
            true // Send as HTML
        );

        if ($result['success']) {
            return [
                'success' => true,
                'message' => "Test email sent successfully to {$testEmail}. Please check your inbox to confirm delivery."
            ];
        } else {
            return [
                'success' => false,
                'message' => "Failed to send test email: " . $result['message']
            ];
        }

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Email test failed: ' . $e->getMessage()];
    }
}

function handleTestSMS() {
    try {
        $testPhone = $_POST['test_phone'] ?? '';

        if (empty($testPhone)) {
            return ['success' => false, 'message' => 'Please enter a phone number for testing'];
        }

        // Initialize SMS service
        $smsService = new SmsService();

        if (!$smsService->isConfigured()) {
            return ['success' => false, 'message' => 'SMS settings not configured. Please configure SMS settings in the Settings page first.'];
        }

        // Test SMS configuration first
        $configTest = $smsService->testConfiguration();

        if (!$configTest['success']) {
            return ['success' => false, 'message' => $configTest['message']];
        }

        // Add debug information for SMS-Gate
        $provider = Config::get('sms_provider');
        if ($provider === 'smsgate') {
            $serverUrl = Config::get('sms_server_url');
            $username = Config::get('sms_username');
            $debugInfo = "SMS-Gate Debug: Server URL: {$serverUrl}, Username: {$username}";
            error_log($debugInfo);
        }

        // Send test SMS
        $testMessage = "Test SMS from " . (Config::get('gym_name') ?: 'MyGym') . " sent at " . date('M j, Y g:i A') . ". SMS configuration is working correctly!";

        $result = $smsService->sendSms($testPhone, $testMessage);

        if ($result['success']) {
            return [
                'success' => true,
                'message' => "Test SMS sent successfully to " . SmsTemplates::formatPhoneNumber($testPhone) . ". Please check your phone to confirm delivery.",
                'details' => $result
            ];
        } else {
            return [
                'success' => false,
                'message' => "Failed to send test SMS: " . $result['message']
            ];
        }

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'SMS test failed: ' . $e->getMessage()];
    }
}

function handleDebugSMS() {
    global $db;

    try {
        $provider = Config::get('sms_provider') ?: 'Not set';
        $serverUrl = Config::get('sms_server_url') ?: 'Not set';
        $username = Config::get('sms_username') ?: 'Not set';
        $password = Config::get('sms_api_key') ?: 'Not set';

        $smsService = new SmsService();
        $isConfigured = $smsService->isConfigured();

        // Check how many members have phone numbers
        $membersWithPhones = $db->fetchAll("SELECT id, first_name, last_name, phone FROM members WHERE phone IS NOT NULL AND phone != '' LIMIT 5");
        $totalMembersWithPhones = $db->fetch("SELECT COUNT(*) as count FROM members WHERE phone IS NOT NULL AND phone != ''")['count'];
        $totalMembers = $db->fetch("SELECT COUNT(*) as count FROM members")['count'];

        $status = 'Unknown';
        $suggestions = '';

        if ($provider === 'smsgate') {
            if (empty(Config::get('sms_server_url'))) {
                $status = 'Missing server URL';
                $suggestions = 'Please enter your Android device IP address (e.g., http://*************:8080)';
            } elseif (empty(Config::get('sms_username'))) {
                $status = 'Missing username';
                $suggestions = 'Please enter the username shown in SMS-Gate app';
            } elseif (empty(Config::get('sms_api_key'))) {
                $status = 'Missing password';
                $suggestions = 'Please enter the password shown in SMS-Gate app';
            } else {
                $status = 'Configuration looks complete';
                $suggestions = 'Try sending a test SMS. Make sure SMS-Gate app is running and "Local Server" is enabled.';
            }
        } elseif ($provider === 'demo') {
            $status = 'Demo mode - no real SMS will be sent';
        } else {
            $status = 'Other provider configured';
        }

        return [
            'success' => true,
            'provider' => $provider,
            'server_url' => $serverUrl,
            'username' => $username,
            'password' => !empty($password),
            'is_configured' => $isConfigured,
            'status' => $status,
            'suggestions' => $suggestions,
            'total_members' => $totalMembers,
            'members_with_phones' => $totalMembersWithPhones,
            'sample_members' => $membersWithPhones
        ];

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Debug failed: ' . $e->getMessage()];
    }
}

function handleTestConnection() {
    try {
        $serverUrl = Config::get('sms_server_url');
        $username = Config::get('sms_username');
        $password = Config::get('sms_api_key');

        if (empty($serverUrl)) {
            return ['success' => false, 'message' => 'No server URL configured'];
        }

        // Parse URL to get host and port
        $urlParts = parse_url($serverUrl);
        $host = $urlParts['host'] ?? '';
        $port = $urlParts['port'] ?? 8080;
        $scheme = $urlParts['scheme'] ?? 'http';

        $results = [];

        // Test 1: DNS Resolution
        $ip = gethostbyname($host);
        if ($ip === $host && !filter_var($host, FILTER_VALIDATE_IP)) {
            $results['dns'] = [
                'success' => false,
                'message' => "DNS resolution failed for {$host}",
                'resolved_ip' => 'N/A'
            ];
        } else {
            $results['dns'] = [
                'success' => true,
                'message' => "DNS resolved successfully",
                'resolved_ip' => $ip
            ];
        }

        // Test 2: Basic connectivity (socket test)
        $startTime = microtime(true);
        $connection = @fsockopen($host, $port, $errno, $errstr, 10);
        $endTime = microtime(true);
        $responseTime = round(($endTime - $startTime) * 1000, 2);

        if ($connection) {
            fclose($connection);
            $results['socket'] = [
                'success' => true,
                'message' => "Socket connection successful to {$host}:{$port}",
                'response_time' => $responseTime . 'ms'
            ];
        } else {
            $results['socket'] = [
                'success' => false,
                'message' => "Socket connection failed: {$errstr} (Error: {$errno})",
                'response_time' => $responseTime . 'ms'
            ];
        }

        // Test 3: HTTP HEAD request (check if web server responds)
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $serverUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $startTime = microtime(true);
        $response = curl_exec($ch);
        $endTime = microtime(true);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $responseTime = round(($endTime - $startTime) * 1000, 2);
        curl_close($ch);

        if ($error) {
            $results['http_head'] = [
                'success' => false,
                'message' => "HTTP HEAD request failed: {$error}",
                'response_time' => $responseTime . 'ms'
            ];
        } else {
            $results['http_head'] = [
                'success' => $httpCode > 0,
                'message' => "HTTP HEAD response: {$httpCode}",
                'response_time' => $responseTime . 'ms',
                'http_code' => $httpCode
            ];
        }

        // Test 4: SMS-Gate API endpoint test (GET /message)
        $messageEndpoint = rtrim($serverUrl, '/') . '/message';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $messageEndpoint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPGET, true);

        // Add authentication if provided
        if (!empty($username) && !empty($password)) {
            curl_setopt($ch, CURLOPT_USERPWD, $username . ':' . $password);
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        }

        $startTime = microtime(true);
        $response = curl_exec($ch);
        $endTime = microtime(true);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $responseTime = round(($endTime - $startTime) * 1000, 2);
        curl_close($ch);

        if ($error) {
            $results['sms_api'] = [
                'success' => false,
                'message' => "SMS-Gate API test failed: {$error}",
                'response_time' => $responseTime . 'ms'
            ];
        } else {
            $isValidResponse = in_array($httpCode, [200, 401, 405]); // 405 = Method Not Allowed is OK for GET on POST endpoint
            $results['sms_api'] = [
                'success' => $isValidResponse,
                'message' => "SMS-Gate API response: {$httpCode}" . ($httpCode === 405 ? ' (Expected - POST endpoint)' : ''),
                'response_time' => $responseTime . 'ms',
                'http_code' => $httpCode,
                'response_preview' => substr($response, 0, 100)
            ];
        }

        // Test 5: Authentication test (if credentials provided)
        $authTest = ['success' => false, 'message' => 'No credentials to test'];
        if (!empty($username) && !empty($password)) {
            // Try a simple authenticated request
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $serverUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_USERPWD, $username . ':' . $password);
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($ch, CURLOPT_NOBODY, true);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                $authTest = [
                    'success' => false,
                    'message' => "Auth test failed: {$error}"
                ];
            } elseif ($httpCode === 401) {
                $authTest = [
                    'success' => false,
                    'message' => "Authentication failed - check username/password"
                ];
            } else {
                $authTest = [
                    'success' => true,
                    'message' => "Authentication appears valid (HTTP {$httpCode})"
                ];
            }
        }
        $results['auth'] = $authTest;

        // Overall assessment and suggestions
        $overallSuccess = $results['socket']['success'] && $results['sms_api']['success'];
        $suggestions = [];

        if (!$results['dns']['success']) {
            $suggestions[] = "DNS issue: Check if the hostname/IP address is correct";
        }
        if (!$results['socket']['success']) {
            $suggestions[] = "Network issue: Check if SMS-Gate app is running and both devices are on same network";
        }
        if (!$results['sms_api']['success']) {
            $suggestions[] = "SMS-Gate issue: Check if the app is properly configured and running";
        }
        if (!$results['auth']['success'] && !empty($username)) {
            $suggestions[] = "Auth issue: Check username and password in SMS-Gate app settings";
        }

        if (empty($suggestions)) {
            $suggestions[] = "Connection looks good! Try sending a test SMS.";
        }

        return [
            'success' => true,
            'server_url' => $serverUrl,
            'host' => $host,
            'port' => $port,
            'scheme' => $scheme,
            'username' => $username,
            'password_set' => !empty($password),
            'tests' => $results,
            'overall_success' => $overallSuccess,
            'suggestions' => $suggestions
        ];

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Connection test failed: ' . $e->getMessage()];
    }
}

function handleExpiredMembershipNotifications() {
    try {
        require_once '../includes/AutoNotifications.php';
        $autoNotifications = new AutoNotifications();

        if (!$autoNotifications->isConfigured()) {
            return ['success' => false, 'message' => 'Email or SMS service not configured'];
        }

        $result = $autoNotifications->sendExpiredMembershipNotifications();
        return $result;

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to send expired membership notifications: ' . $e->getMessage()];
    }
}

// Get expiring members (next 30 days)
$expiringMembers = $db->fetchAll("
    SELECT m.*, p.name as plan_name,
           DATEDIFF(m.end_date, CURDATE()) as days_until_expiry
    FROM members m
    LEFT JOIN plans p ON m.plan_id = p.id
    WHERE m.status = 'active' 
    AND m.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
    ORDER BY m.end_date ASC
");

// Get overdue members
$overdueMembers = $db->fetchAll("
    SELECT m.*, p.name as plan_name,
           DATEDIFF(CURDATE(), m.end_date) as days_overdue
    FROM members m
    LEFT JOIN plans p ON m.plan_id = p.id
    WHERE m.status = 'active' 
    AND m.end_date < CURDATE()
    ORDER BY m.end_date ASC
");

// Get new members (last 7 days)
$newMembers = $db->fetchAll("
    SELECT m.*, p.name as plan_name
    FROM members m
    LEFT JOIN plans p ON m.plan_id = p.id
    WHERE m.created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    ORDER BY m.created_at DESC
");

$pageTitle = 'Notifications';
include '../includes/header.php';

// Check email configuration
$emailService = new EmailService();
$emailConfigured = $emailService->isConfigured();
?>

<div class="space-y-6">

    <?php if (!$emailConfigured): ?>
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
                <i class="fas fa-exclamation-triangle text-yellow-400 mt-0.5 mr-3"></i>
                <div>
                    <h3 class="text-sm font-medium text-yellow-800">Email Not Configured</h3>
                    <p class="mt-1 text-sm text-yellow-700">
                        Email notifications are not available because SMTP settings are not configured.
                        <a href="settings.php" class="font-medium underline hover:text-yellow-600">Configure email settings</a>
                        to enable email notifications.
                    </p>
                </div>
            </div>
        </div>
    <?php endif; ?>
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Notifications</h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Send notifications and manage member communications</p>
        </div>
    </div>

    <!-- Email Configuration Status -->
    <?php
    $emailService = new EmailService();
    $emailConfig = $emailService->getConfigSummary();
    ?>
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-envelope text-blue-500 mr-2"></i>
                Email Configuration Status
            </h2>
            <button onclick="testEmailConfig()"
                    id="testEmailBtn"
                    class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                <i class="fas fa-vial mr-2"></i>
                Test Email
            </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="font-medium text-gray-900 dark:text-white mb-3">Configuration Status</h3>
                <div class="space-y-2">
                    <div class="flex items-center">
                        <i class="fas fa-<?= $emailConfig['configured'] ? 'check-circle text-green-500' : 'times-circle text-red-500' ?> mr-2"></i>
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                            Email Settings: <?= $emailConfig['configured'] ? 'Configured' : 'Not Configured' ?>
                        </span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-<?= $emailConfig['mail_function_available'] ? 'check-circle text-green-500' : 'times-circle text-red-500' ?> mr-2"></i>
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                            Mail Function: <?= $emailConfig['mail_function_available'] ? 'Available' : 'Not Available' ?>
                        </span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-envelope mr-2 text-gray-400"></i>
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                            From Email: <?= htmlspecialchars($emailConfig['from_email'] ?: 'Not set') ?>
                        </span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-user mr-2 text-gray-400"></i>
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                            From Name: <?= htmlspecialchars($emailConfig['from_name'] ?: 'Not set') ?>
                        </span>
                    </div>
                </div>
            </div>

            <div>
                <h3 class="font-medium text-gray-900 dark:text-white mb-3">Quick Actions</h3>
                <div class="space-y-2">
                    <?php if (!$emailConfig['configured']): ?>
                        <a href="../pages/settings.php"
                           class="inline-flex items-center px-3 py-2 bg-yellow-600 hover:bg-yellow-700 text-white text-sm rounded-lg transition duration-200">
                            <i class="fas fa-cog mr-2"></i>
                            Configure Email Settings
                        </a>
                    <?php else: ?>
                        <div class="text-sm text-green-600 dark:text-green-400">
                            <i class="fas fa-check-circle mr-2"></i>
                            Ready to send emails!
                        </div>
                    <?php endif; ?>
                </div>

                <div id="emailTestResult" class="hidden mt-4 p-3 rounded-lg border">
                    <div id="emailTestMessage"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Expiring Soon</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= count($expiringMembers) ?></p>
                </div>
                <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-600 dark:text-yellow-400"></i>
                </div>
            </div>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Next 30 days</p>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Overdue</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= count($overdueMembers) ?></p>
                </div>
                <div class="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                </div>
            </div>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Past due date</p>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">New Members</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= count($newMembers) ?></p>
                </div>
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-user-plus text-green-600 dark:text-green-400"></i>
                </div>
            </div>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Last 7 days</p>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Send Email Notification -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-envelope text-blue-600 dark:text-blue-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Send Email Notification</h3>
            </div>

            <form method="POST" action="?action=send_email" class="space-y-4">
                <?= Auth::csrfField() ?>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Recipients</label>
                    <div class="space-y-2 max-h-32 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-lg p-3">
                        <label class="flex items-center">
                            <input type="checkbox" id="select-all-email" class="mr-2">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Select All</span>
                        </label>
                        <?php foreach ($expiringMembers as $member): ?>
                            <label class="flex items-center">
                                <input type="checkbox" name="member_ids[]" value="<?= $member['id'] ?>" class="member-checkbox-email mr-2">
                                <span class="text-sm text-gray-600 dark:text-gray-400">
                                    <?= htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) ?>
                                    (Expires: <?= formatDate($member['end_date']) ?>)
                                </span>
                            </label>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Email Template Selector -->
                <div>
                    <label for="email_template" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-palette mr-1"></i>Email Template
                    </label>
                    <select id="email_template"
                            name="email_template"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                            onchange="updateEmailTemplate()">
                        <option value="">Select a template...</option>
                        <?php
                        $templates = EmailTemplates::getAvailableTemplates();
                        foreach ($templates as $key => $template):
                        ?>
                            <option value="<?= $key ?>" data-icon="<?= $template['icon'] ?>" data-color="<?= $template['color'] ?>">
                                <?= htmlspecialchars($template['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="mt-2 flex gap-2">
                        <button type="button"
                                onclick="showTemplatePreview()"
                                class="text-xs px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md transition-colors">
                            <i class="fas fa-eye mr-1"></i>Preview
                        </button>
                        <p class="text-xs text-gray-500 dark:text-gray-400 flex-1">
                            Choose a pre-designed template or create a custom message
                        </p>
                    </div>
                </div>

                <div>
                    <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Subject</label>
                    <input type="text"
                           id="subject"
                           name="subject"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                           placeholder="Membership Renewal Reminder"
                           required>
                </div>

                <div>
                    <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Message</label>
                    <textarea id="message"
                              name="message"
                              rows="4"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                              placeholder="Your membership is expiring soon. Please renew to continue enjoying our services."
                              required></textarea>
                </div>

                <button type="submit"
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                    <i class="fas fa-paper-plane mr-2"></i>
                    Send Email
                </button>
            </form>
        </div>

        <!-- Send SMS Notification -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-sms text-green-600 dark:text-green-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Send SMS Notification</h3>
            </div>

            <form method="POST" action="?action=send_sms" class="space-y-4">
                <?= Auth::csrfField() ?>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Recipients</label>
                    <div class="space-y-2 max-h-32 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-lg p-3">
                        <label class="flex items-center">
                            <input type="checkbox" id="select-all-sms" class="mr-2">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Select All</span>
                        </label>
                        <?php foreach ($expiringMembers as $member): ?>
                            <label class="flex items-center">
                                <input type="checkbox" name="member_ids[]" value="<?= $member['id'] ?>" class="member-checkbox-sms mr-2">
                                <span class="text-sm text-gray-600 dark:text-gray-400">
                                    <?= htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) ?>
                                    (<?= htmlspecialchars($member['phone']) ?>)
                                </span>
                            </label>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div>
                    <label for="sms_template" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SMS Template</label>
                    <select id="sms_template"
                            name="sms_template"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white mb-3"
                            onchange="updateSmsTemplate()">
                        <option value="custom">Custom Message</option>
                        <?php foreach (SmsTemplates::getAvailableTemplates() as $key => $template): ?>
                            <option value="<?= $key ?>" data-description="<?= htmlspecialchars($template['description']) ?>">
                                <?= htmlspecialchars($template['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div>
                    <label for="sms_message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Message</label>
                    <textarea id="sms_message"
                              name="sms_message"
                              rows="4"
                              maxlength="500"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                              placeholder="Enter your custom SMS message here. Use {member_name} and {gym_name} for personalization."
                              oninput="updateSmsCharacterCount()"></textarea>
                    <div class="flex justify-between items-center mt-1">
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            <span id="sms-char-count">0</span> characters |
                            <span id="sms-count">1</span> SMS
                        </p>
                        <div id="sms-template-preview" class="text-xs text-blue-600 dark:text-blue-400 hidden">
                            Template will be used
                        </div>
                    </div>
                </div>

                <button type="submit"
                        class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                    <i class="fas fa-mobile-alt mr-2"></i>
                    Send SMS
                </button>
            </form>
        </div>

    </div>

    <!-- Automated Notifications -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-3">
                <i class="fas fa-robot text-purple-600 dark:text-purple-400"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Automated Notifications</h3>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Welcome Notifications -->
            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                <div class="flex items-center mb-2">
                    <i class="fas fa-user-plus text-blue-600 dark:text-blue-400 mr-2"></i>
                    <h4 class="font-medium text-blue-900 dark:text-blue-100">Welcome Messages</h4>
                </div>
                <p class="text-sm text-blue-700 dark:text-blue-300 mb-3">Automatically sent when new members join</p>
                <div class="text-xs text-blue-600 dark:text-blue-400">
                    <i class="fas fa-check-circle mr-1"></i>
                    Auto-triggered on member creation
                </div>
            </div>

            <!-- Payment Confirmations -->
            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                <div class="flex items-center mb-2">
                    <i class="fas fa-check-circle text-green-600 dark:text-green-400 mr-2"></i>
                    <h4 class="font-medium text-green-900 dark:text-green-100">Payment Confirmations</h4>
                </div>
                <p class="text-sm text-green-700 dark:text-green-300 mb-3">Automatically sent after successful payments</p>
                <div class="text-xs text-green-600 dark:text-green-400">
                    <i class="fas fa-check-circle mr-1"></i>
                    Auto-triggered on payment processing
                </div>
            </div>

            <!-- Expired Memberships -->
            <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                <div class="flex items-center mb-2">
                    <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 mr-2"></i>
                    <h4 class="font-medium text-red-900 dark:text-red-100">Expired Memberships</h4>
                </div>
                <p class="text-sm text-red-700 dark:text-red-300 mb-3">Send notifications to expired members</p>
                <button onclick="sendExpiredMembershipNotifications()"
                        class="w-full bg-red-600 hover:bg-red-700 text-white text-xs font-medium py-2 px-3 rounded transition duration-200">
                    <i class="fas fa-paper-plane mr-1"></i>
                    Send Now
                </button>
            </div>
        </div>

        <div id="auto-notification-result" class="mt-4 hidden"></div>
    </div>

    <!-- Member Lists -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Expiring Members -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    <i class="fas fa-clock text-yellow-500 mr-2"></i>
                    Expiring Soon (<?= count($expiringMembers) ?>)
                </h3>
            </div>

            <div class="max-h-96 overflow-y-auto">
                <?php foreach ($expiringMembers as $member): ?>
                <div class="px-6 py-4 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-medium text-gray-900 dark:text-white">
                                <?= htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) ?>
                            </p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                <?= htmlspecialchars($member['plan_name']) ?>
                            </p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-yellow-600 dark:text-yellow-400">
                                <?= $member['days_until_expiry'] ?> days
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                <?= formatDate($member['end_date']) ?>
                            </p>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>

                <?php if (empty($expiringMembers)): ?>
                <div class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                    <i class="fas fa-check-circle text-2xl mb-2"></i>
                    <p>No memberships expiring soon</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Overdue Members -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                    Overdue (<?= count($overdueMembers) ?>)
                </h3>
            </div>

            <div class="max-h-96 overflow-y-auto">
                <?php foreach ($overdueMembers as $member): ?>
                <div class="px-6 py-4 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-medium text-gray-900 dark:text-white">
                                <?= htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) ?>
                            </p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                <?= htmlspecialchars($member['plan_name']) ?>
                            </p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-red-600 dark:text-red-400">
                                <?= $member['days_overdue'] ?> days overdue
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                <?= formatDate($member['end_date']) ?>
                            </p>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>

                <?php if (empty($overdueMembers)): ?>
                <div class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                    <i class="fas fa-check-circle text-2xl mb-2"></i>
                    <p>No overdue memberships</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// Select all functionality for email
document.getElementById('select-all-email').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.member-checkbox-email');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Select all functionality for SMS
document.getElementById('select-all-sms').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.member-checkbox-sms');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Character counter for SMS
document.getElementById('sms_message').addEventListener('input', function() {
    const maxLength = 160;
    const currentLength = this.value.length;
    const remaining = maxLength - currentLength;

    // Update character count display
    const counter = this.parentElement.querySelector('.text-xs');
    counter.textContent = `${remaining} characters remaining`;

    if (remaining < 0) {
        counter.classList.add('text-red-500');
        counter.classList.remove('text-gray-500');
    } else {
        counter.classList.remove('text-red-500');
        counter.classList.add('text-gray-500');
    }
});

// Email configuration test function
function testEmailConfig() {
    const btn = document.getElementById('testEmailBtn');
    const resultDiv = document.getElementById('emailTestResult');
    const messageDiv = document.getElementById('emailTestMessage');

    // Show loading state
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing...';

    resultDiv.className = 'block mt-4 p-3 rounded-lg border border-blue-200 bg-blue-50 dark:bg-blue-900/20';
    messageDiv.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing email configuration...';

    // Create a test email request
    const formData = new FormData();
    formData.append('action', 'test_email');

    fetch('notifications.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.className = 'block mt-4 p-3 rounded-lg border border-green-200 bg-green-50 dark:bg-green-900/20';
            messageDiv.innerHTML = `
                <div class="flex items-center text-green-800 dark:text-green-300">
                    <i class="fas fa-check-circle mr-2"></i>
                    <div>
                        <div class="font-medium">Email test successful!</div>
                        <div class="text-sm mt-1">${data.message}</div>
                    </div>
                </div>
            `;
        } else {
            resultDiv.className = 'block mt-4 p-3 rounded-lg border border-red-200 bg-red-50 dark:bg-red-900/20';
            messageDiv.innerHTML = `
                <div class="flex items-center text-red-800 dark:text-red-300">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <div>
                        <div class="font-medium">Email test failed!</div>
                        <div class="text-sm mt-1">${data.message}</div>
                    </div>
                </div>
            `;
        }
    })
    .catch(error => {
        resultDiv.className = 'block mt-4 p-3 rounded-lg border border-red-200 bg-red-50 dark:bg-red-900/20';
        messageDiv.innerHTML = `
            <div class="flex items-center text-red-800 dark:text-red-300">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <div>
                    <div class="font-medium">Network error!</div>
                    <div class="text-sm mt-1">${error.message}</div>
                </div>
            </div>
        `;
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-vial mr-2"></i>Test Email';
    });
}

// Email Template Handling
function updateEmailTemplate() {
    const templateSelect = document.getElementById('email_template');
    const subjectField = document.getElementById('subject');
    const messageField = document.getElementById('message');

    const selectedTemplate = templateSelect.value;

    if (!selectedTemplate) {
        // Clear fields if no template selected
        subjectField.value = '';
        messageField.value = '';
        return;
    }

    // Template-specific subjects and placeholders
    const templateData = {
        'welcome': {
            subject: 'Welcome to <?= Config::get("gym_name") ?: "MyGym" ?>! 🎉',
            placeholder: 'Welcome message will be automatically generated using the template...'
        },
        'membership_expiry': {
            subject: 'Membership Renewal Reminder - <?= Config::get("gym_name") ?: "MyGym" ?>',
            placeholder: 'Membership expiry reminder will be automatically generated...'
        },
        'payment_reminder': {
            subject: 'Payment Reminder - <?= Config::get("gym_name") ?: "MyGym" ?>',
            placeholder: 'Payment reminder will be automatically generated...'
        },
        'class_announcement': {
            subject: 'New Class Alert - <?= Config::get("gym_name") ?: "MyGym" ?>',
            placeholder: 'Class announcement will be automatically generated...'
        },
        'promotion': {
            subject: '🎉 Special Offer - <?= Config::get("gym_name") ?: "MyGym" ?>',
            placeholder: 'Promotion details will be automatically generated...'
        },
        'maintenance': {
            subject: 'Maintenance Notice - <?= Config::get("gym_name") ?: "MyGym" ?>',
            placeholder: 'Maintenance notice will be automatically generated...'
        },
        'birthday': {
            subject: '🎂 Happy Birthday from <?= Config::get("gym_name") ?: "MyGym" ?>!',
            placeholder: 'Birthday wishes will be automatically generated...'
        },
        'achievement': {
            subject: '🏆 Congratulations! - <?= Config::get("gym_name") ?: "MyGym" ?>',
            placeholder: 'Achievement congratulations will be automatically generated...'
        },
        'newsletter': {
            subject: '📰 <?= Config::get("gym_name") ?: "MyGym" ?> Newsletter - <?= date("F Y") ?>',
            placeholder: 'Newsletter content will be automatically generated...'
        },
        'custom': {
            subject: 'Message from <?= Config::get("gym_name") ?: "MyGym" ?>',
            placeholder: 'Enter your custom message here...'
        }
    };

    if (templateData[selectedTemplate]) {
        subjectField.value = templateData[selectedTemplate].subject;
        messageField.placeholder = templateData[selectedTemplate].placeholder;

        if (selectedTemplate === 'custom') {
            messageField.value = '';
            messageField.focus();
        } else {
            messageField.value = 'This email will use the ' + templateSelect.options[templateSelect.selectedIndex].text + ' template.';
        }
    }
}

// Template preview functionality
function showTemplatePreview() {
    const templateSelect = document.getElementById('email_template');
    const selectedTemplate = templateSelect.value;

    if (!selectedTemplate || selectedTemplate === 'custom') {
        return;
    }

    // Create preview modal (simplified)
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">Template Preview</h3>
                <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="text-sm text-gray-600 mb-4">
                Preview of: <strong>${templateSelect.options[templateSelect.selectedIndex].text}</strong>
            </div>
            <div class="border rounded p-4 bg-gray-50 dark:bg-gray-700">
                <p class="text-sm">This is a preview of the selected email template. The actual email will include personalized content for each recipient.</p>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

// SMS Template Handling
function updateSmsTemplate() {
    const templateSelect = document.getElementById('sms_template');
    const messageField = document.getElementById('sms_message');
    const templatePreview = document.getElementById('sms-template-preview');

    const selectedTemplate = templateSelect.value;

    if (selectedTemplate === 'custom') {
        messageField.placeholder = 'Enter your custom SMS message here. Use {member_name} and {gym_name} for personalization.';
        messageField.required = true;
        messageField.value = '';
        templatePreview.classList.add('hidden');
    } else {
        // Generate preview of template
        const templateData = {
            'welcome': 'Welcome to MyGym, {member_name}! We\'re excited to have you join our fitness family...',
            'membership_expiry': 'Hi {member_name}, your MyGym membership expires soon. Don\'t miss out...',
            'payment_reminder': 'Payment reminder from MyGym: Hi {member_name}, your payment is due...',
            'payment_confirmation': 'Payment confirmed! Thank you {member_name} for your payment...',
            'class_announcement': 'New class alert at MyGym! Join our exciting new fitness class...',
            'promotion': 'Special offer from MyGym! {member_name}, enjoy our limited time offer...',
            'maintenance': 'Maintenance notice from MyGym: We\'ll be closed for improvements...',
            'birthday': 'Happy Birthday {member_name}! The entire MyGym family wishes you...',
            'achievement': 'Congratulations {member_name}! You\'ve achieved your fitness milestone...',
            'appointment_reminder': 'Reminder: Your personal training session at MyGym is scheduled...'
        };

        messageField.placeholder = templateData[selectedTemplate] || 'Template message will be generated automatically...';
        messageField.required = false;
        messageField.value = '';
        templatePreview.classList.remove('hidden');
    }

    updateSmsCharacterCount();
}

// SMS Character Counter
function updateSmsCharacterCount() {
    const messageField = document.getElementById('sms_message');
    const charCountSpan = document.getElementById('sms-char-count');
    const smsCountSpan = document.getElementById('sms-count');

    const message = messageField.value;
    const charCount = message.length;

    // Calculate SMS count (160 chars for single SMS, 153 for multi-part)
    let smsCount;
    if (charCount <= 160) {
        smsCount = 1;
    } else if (charCount <= 306) {
        smsCount = 2;
    } else if (charCount <= 459) {
        smsCount = 3;
    } else {
        smsCount = Math.ceil(charCount / 153);
    }

    charCountSpan.textContent = charCount;
    smsCountSpan.textContent = smsCount;

    // Color coding for character count
    if (charCount <= 160) {
        charCountSpan.className = 'text-green-600';
    } else if (charCount <= 306) {
        charCountSpan.className = 'text-yellow-600';
    } else {
        charCountSpan.className = 'text-red-600';
    }
}

// Test SMS functionality
document.getElementById('test-sms-form').addEventListener('submit', function(e) {
    e.preventDefault();
    testSmsConfiguration();
});

function testSmsConfiguration() {
    const form = document.getElementById('test-sms-form');
    const btn = form.querySelector('button[type="submit"]');
    const resultDiv = document.getElementById('sms-test-result');
    const testPhone = document.getElementById('test_phone').value;

    if (!testPhone) {
        showSmsTestResult('error', 'Please enter a phone number for testing.');
        return;
    }

    // Show loading state
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending Test SMS...';
    resultDiv.classList.add('hidden');

    // Send test SMS
    fetch('?action=test_sms', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `test_phone=${encodeURIComponent(testPhone)}&csrf_token=<?= Auth::generateCsrfToken() ?>`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSmsTestResult('success', data.message);
        } else {
            showSmsTestResult('error', data.message);
        }
    })
    .catch(error => {
        showSmsTestResult('error', 'Failed to send test SMS: ' + error.message);
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>Send Test SMS';
    });
}

function showSmsTestResult(type, message) {
    const resultDiv = document.getElementById('sms-test-result');
    const bgColor = type === 'success' ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800';
    const icon = type === 'success' ? 'fa-check-circle text-green-500' : 'fa-exclamation-circle text-red-500';

    resultDiv.innerHTML = `
        <div class="p-4 rounded-lg border ${bgColor}">
            <div class="flex items-start">
                <i class="fas ${icon} mt-0.5 mr-3"></i>
                <span class="text-sm">${message}</span>
            </div>
        </div>
    `;

    resultDiv.classList.remove('hidden');
}

// Show SMS debug information
function showSmsDebugInfo() {
    const resultDiv = document.getElementById('sms-test-result');

    // Get current SMS settings from PHP
    fetch('?action=debug_sms', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `csrf_token=<?= Auth::generateCsrfToken() ?>`
    })
    .then(response => response.json())
    .then(data => {
        let membersInfo = '';
        if (data.sample_members && data.sample_members.length > 0) {
            membersInfo = '<div class="mt-3"><strong>Sample Members with Phone Numbers:</strong><ul class="list-disc list-inside text-xs">';
            data.sample_members.forEach(member => {
                membersInfo += `<li>${member.first_name} ${member.last_name}: ${member.phone}</li>`;
            });
            membersInfo += '</ul></div>';
        }

        let debugHtml = `
            <div class="p-4 rounded-lg border bg-gray-50 border-gray-200 text-gray-800">
                <h4 class="font-semibold mb-2">SMS Configuration Debug Info:</h4>
                <div class="text-sm space-y-1">
                    <p><strong>Provider:</strong> ${data.provider || 'Not set'}</p>
                    <p><strong>Server URL:</strong> ${data.server_url || 'Not set'}</p>
                    <p><strong>Username:</strong> ${data.username || 'Not set'}</p>
                    <p><strong>Password:</strong> ${data.password ? '***SET***' : 'Not set'}</p>
                    <p><strong>Is Configured:</strong> ${data.is_configured ? 'Yes' : 'No'}</p>
                    <p><strong>Status:</strong> ${data.status}</p>
                    <p><strong>Total Members:</strong> ${data.total_members || 0}</p>
                    <p><strong>Members with Phone Numbers:</strong> ${data.members_with_phones || 0}</p>
                </div>
                ${membersInfo}
                ${data.suggestions ? `<div class="mt-3 p-2 bg-blue-100 rounded text-blue-800"><strong>Suggestions:</strong><br>${data.suggestions}</div>` : ''}
            </div>
        `;

        resultDiv.innerHTML = debugHtml;
        resultDiv.classList.remove('hidden');
    })
    .catch(error => {
        showSmsTestResult('error', 'Failed to get debug info: ' + error.message);
    });
}

// Test SMS-Gate connection
function testSmsGateConnection() {
    const resultDiv = document.getElementById('sms-test-result');

    // Show loading
    resultDiv.innerHTML = `
        <div class="p-4 rounded-lg border bg-blue-50 border-blue-200 text-blue-800">
            <div class="flex items-center">
                <i class="fas fa-spinner fa-spin mr-3"></i>
                <span>Testing connection to SMS-Gate server...</span>
            </div>
        </div>
    `;
    resultDiv.classList.remove('hidden');

    fetch('?action=test_connection', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `csrf_token=<?= Auth::generateCsrfToken() ?>`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            let testsHtml = '';
            if (data.tests) {
                testsHtml = '<div class="mt-3"><strong>Detailed Connection Tests:</strong><ul class="list-none text-sm mt-2 space-y-1">';

                // DNS Test
                if (data.tests.dns) {
                    const test = data.tests.dns;
                    const icon = test.success ? '✅' : '❌';
                    testsHtml += `<li class="flex items-start"><span class="mr-2">${icon}</span><div><strong>DNS Resolution:</strong> ${test.message}`;
                    if (test.resolved_ip && test.resolved_ip !== 'N/A') {
                        testsHtml += ` (${test.resolved_ip})`;
                    }
                    testsHtml += `</div></li>`;
                }

                // Socket Test
                if (data.tests.socket) {
                    const test = data.tests.socket;
                    const icon = test.success ? '✅' : '❌';
                    testsHtml += `<li class="flex items-start"><span class="mr-2">${icon}</span><div><strong>Socket Connection:</strong> ${test.message} <span class="text-gray-500">(${test.response_time})</span></div></li>`;
                }

                // HTTP HEAD Test
                if (data.tests.http_head) {
                    const test = data.tests.http_head;
                    const icon = test.success ? '✅' : '❌';
                    testsHtml += `<li class="flex items-start"><span class="mr-2">${icon}</span><div><strong>HTTP Server:</strong> ${test.message} <span class="text-gray-500">(${test.response_time})</span></div></li>`;
                }

                // SMS API Test
                if (data.tests.sms_api) {
                    const test = data.tests.sms_api;
                    const icon = test.success ? '✅' : '❌';
                    testsHtml += `<li class="flex items-start"><span class="mr-2">${icon}</span><div><strong>SMS-Gate API:</strong> ${test.message} <span class="text-gray-500">(${test.response_time})</span></div></li>`;
                }

                // Auth Test
                if (data.tests.auth) {
                    const test = data.tests.auth;
                    const icon = test.success ? '✅' : '❌';
                    testsHtml += `<li class="flex items-start"><span class="mr-2">${icon}</span><div><strong>Authentication:</strong> ${test.message}</div></li>`;
                }

                testsHtml += '</ul></div>';
            }

            const overallStatus = data.overall_success ?
                '<div class="mt-3 p-3 bg-green-100 rounded text-green-800"><strong>🎉 Connection Successful!</strong><br>SMS-Gate server is reachable and responding correctly.</div>' :
                '<div class="mt-3 p-3 bg-red-100 rounded text-red-800"><strong>⚠️ Connection Issues Detected!</strong><br>There are problems connecting to SMS-Gate server.</div>';

            let suggestionsHtml = '';
            if (data.suggestions && data.suggestions.length > 0) {
                suggestionsHtml = '<div class="mt-3 p-3 bg-yellow-100 rounded text-yellow-800"><strong>💡 Suggestions:</strong><ul class="list-disc list-inside mt-1">';
                data.suggestions.forEach(suggestion => {
                    suggestionsHtml += `<li>${suggestion}</li>`;
                });
                suggestionsHtml += '</ul></div>';
            }

            resultDiv.innerHTML = `
                <div class="p-4 rounded-lg border bg-gray-50 border-gray-200 text-gray-800">
                    <h4 class="font-semibold mb-3">🔍 SMS-Gate Connection Analysis</h4>
                    <div class="text-sm mb-3 bg-white p-2 rounded border">
                        <p><strong>Server URL:</strong> ${data.server_url}</p>
                        <p><strong>Host:</strong> ${data.host} | <strong>Port:</strong> ${data.port} | <strong>Protocol:</strong> ${data.scheme}</p>
                        <p><strong>Username:</strong> ${data.username || 'Not set'} | <strong>Password:</strong> ${data.password_set ? '***SET***' : 'Not set'}</p>
                    </div>
                    ${testsHtml}
                    ${overallStatus}
                    ${suggestionsHtml}
                </div>
            `;
        } else {
            showSmsTestResult('error', data.message);
        }
    })
    .catch(error => {
        showSmsTestResult('error', 'Connection test failed: ' + error.message);
    });
}

// Send expired membership notifications
function sendExpiredMembershipNotifications() {
    const resultDiv = document.getElementById('auto-notification-result');

    // Show loading
    resultDiv.innerHTML = `
        <div class="p-4 rounded-lg border bg-blue-50 border-blue-200 text-blue-800">
            <div class="flex items-center">
                <i class="fas fa-spinner fa-spin mr-3"></i>
                <span>Sending expired membership notifications...</span>
            </div>
        </div>
    `;
    resultDiv.classList.remove('hidden');

    fetch('?action=send_expired_notifications', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `csrf_token=<?= Auth::generateCsrfToken() ?>`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            let detailsHtml = '';
            if (data.email_count !== undefined && data.sms_count !== undefined) {
                detailsHtml = `
                    <div class="mt-2 text-sm">
                        <p><strong>Details:</strong></p>
                        <ul class="list-disc list-inside ml-4">
                            <li>${data.email_count} emails sent</li>
                            <li>${data.sms_count} SMS sent</li>
                            <li>${data.total_members} expired members processed</li>
                        </ul>
                    </div>
                `;
            }

            resultDiv.innerHTML = `
                <div class="p-4 rounded-lg border bg-green-50 border-green-200 text-green-800">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-3"></i>
                        <span><strong>Success!</strong> ${data.message}</span>
                    </div>
                    ${detailsHtml}
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="p-4 rounded-lg border bg-red-50 border-red-200 text-red-800">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-3"></i>
                        <span><strong>Error:</strong> ${data.message}</span>
                    </div>
                </div>
            `;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `
            <div class="p-4 rounded-lg border bg-red-50 border-red-200 text-red-800">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-3"></i>
                    <span><strong>Error:</strong> Failed to send notifications: ${error.message}</span>
                </div>
            </div>
        `;
    });
}

// Initialize character count on page load
document.addEventListener('DOMContentLoaded', function() {
    updateSmsCharacterCount();
});
</script>

<?php include '../includes/footer.php'; ?>
