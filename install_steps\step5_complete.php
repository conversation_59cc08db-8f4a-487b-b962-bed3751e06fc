<!-- Step 5: Installation Complete -->
<div class="text-center mb-8">
    <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <i class="fas fa-check text-4xl text-green-600"></i>
    </div>
    <h2 class="text-3xl font-bold text-gray-900 mb-2">Installation Complete!</h2>
    <p class="text-gray-600">MyGym Management System has been successfully installed</p>
</div>

<div class="space-y-6">
    <!-- Success Message -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
        <div class="flex">
            <i class="fas fa-check-circle text-green-400 mt-1 mr-3"></i>
            <div class="flex-1">
                <h3 class="text-green-800 font-medium text-lg mb-2">Installation Successful!</h3>
                <p class="text-green-700 mb-4"><?= htmlspecialchars($result['message']) ?></p>
                
                <div class="bg-white rounded-lg p-4 border border-green-200">
                    <h4 class="font-medium text-green-800 mb-2">Your Admin Account:</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-green-600 font-medium">Name:</span>
                            <span class="text-green-800"><?= htmlspecialchars($result['admin_name']) ?></span>
                        </div>
                        <div>
                            <span class="text-green-600 font-medium">Email:</span>
                            <span class="text-green-800"><?= htmlspecialchars($result['admin_email']) ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Next Steps -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 class="text-blue-800 font-medium text-lg mb-4 flex items-center">
            <i class="fas fa-list-check mr-2"></i>
            Next Steps
        </h3>
        <div class="space-y-3">
            <div class="flex items-start">
                <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</div>
                <div>
                    <h4 class="font-medium text-blue-800">Login to Your System</h4>
                    <p class="text-blue-700 text-sm">Use your admin credentials to access the dashboard</p>
                </div>
            </div>
            <div class="flex items-start">
                <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</div>
                <div>
                    <h4 class="font-medium text-blue-800">Configure Settings</h4>
                    <p class="text-blue-700 text-sm">Set up email, SMS, and other system preferences</p>
                </div>
            </div>
            <div class="flex items-start">
                <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</div>
                <div>
                    <h4 class="font-medium text-blue-800">Add Membership Plans</h4>
                    <p class="text-blue-700 text-sm">Create your gym's membership plans and pricing</p>
                </div>
            </div>
            <div class="flex items-start">
                <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</div>
                <div>
                    <h4 class="font-medium text-blue-800">Start Adding Members</h4>
                    <p class="text-blue-700 text-sm">Begin registering your gym members</p>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <h3 class="text-gray-800 font-medium text-lg mb-4 flex items-center">
            <i class="fas fa-info-circle mr-2"></i>
            System Information
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
                <span class="text-gray-600 font-medium">Version:</span>
                <span class="text-gray-800">MyGym 2.0</span>
            </div>
            <div>
                <span class="text-gray-600 font-medium">Installation Date:</span>
                <span class="text-gray-800"><?= date('Y-m-d H:i:s') ?></span>
            </div>
            <div>
                <span class="text-gray-600 font-medium">PHP Version:</span>
                <span class="text-gray-800"><?= PHP_VERSION ?></span>
            </div>
            <div>
                <span class="text-gray-600 font-medium">Database:</span>
                <span class="text-gray-800">MySQL Connected</span>
            </div>
        </div>
    </div>

    <!-- Security Recommendations -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h3 class="text-yellow-800 font-medium text-lg mb-4 flex items-center">
            <i class="fas fa-shield-alt mr-2"></i>
            Security Recommendations
        </h3>
        <ul class="space-y-2 text-sm text-yellow-700">
            <li class="flex items-start">
                <i class="fas fa-check text-yellow-600 mr-2 mt-0.5"></i>
                <span>Delete the installation files for security (install.php and install_steps/ folder)</span>
            </li>
            <li class="flex items-start">
                <i class="fas fa-check text-yellow-600 mr-2 mt-0.5"></i>
                <span>Set up regular database backups</span>
            </li>
            <li class="flex items-start">
                <i class="fas fa-check text-yellow-600 mr-2 mt-0.5"></i>
                <span>Configure SSL certificate for secure connections</span>
            </li>
            <li class="flex items-start">
                <i class="fas fa-check text-yellow-600 mr-2 mt-0.5"></i>
                <span>Keep your system updated with the latest security patches</span>
            </li>
        </ul>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="index.php" 
           class="bg-green-600 hover:bg-green-700 text-white font-medium py-4 px-8 rounded-lg transition duration-200 flex items-center justify-center">
            <i class="fas fa-sign-in-alt mr-2"></i>
            Login to MyGym
        </a>
        
        <a href="dashboard.php" 
           class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-4 px-8 rounded-lg transition duration-200 flex items-center justify-center">
            <i class="fas fa-tachometer-alt mr-2"></i>
            Go to Dashboard
        </a>
    </div>
</div>

<script>
// Auto-redirect after 10 seconds
let countdown = 10;
const redirectTimer = setInterval(function() {
    countdown--;
    if (countdown <= 0) {
        clearInterval(redirectTimer);
        window.location.href = 'index.php';
    }
}, 1000);

// Show success animation
document.addEventListener('DOMContentLoaded', function() {
    // Add celebration effect
    const checkIcon = document.querySelector('.fa-check');
    if (checkIcon) {
        checkIcon.style.transform = 'scale(0)';
        setTimeout(function() {
            checkIcon.style.transition = 'transform 0.5s ease-out';
            checkIcon.style.transform = 'scale(1)';
        }, 500);
    }
});
</script>
