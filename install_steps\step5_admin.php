<!-- Step 5: Admin Account Creation -->
<div class="text-center mb-6">
    <i class="fas fa-user-shield text-4xl text-blue-600 mb-4"></i>
    <h2 class="text-2xl font-bold text-gray-900 mb-2">Create Admin Account</h2>
    <p class="text-gray-600">Set up your administrator account to manage the system</p>
</div>

<form method="POST" class="space-y-6" id="adminForm">
    <input type="hidden" name="step" value="5">
    
    <div class="space-y-6">
        <div>
            <label for="admin_name" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-user mr-2"></i>Full Name
            </label>
            <input type="text" 
                   id="admin_name" 
                   name="admin_name" 
                   value="<?= htmlspecialchars($_POST['admin_name'] ?? '') ?>"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                   placeholder="e.g., <PERSON>, <PERSON>"
                   required>
            <p class="mt-1 text-sm text-gray-500">This will be displayed as the administrator name</p>
        </div>
        
        <div>
            <label for="admin_email" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-envelope mr-2"></i>Email Address
            </label>
            <input type="email" 
                   id="admin_email" 
                   name="admin_email" 
                   value="<?= htmlspecialchars($_POST['admin_email'] ?? '') ?>"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                   placeholder="<EMAIL>"
                   required>
            <p class="mt-1 text-sm text-gray-500">This will be your login email address</p>
        </div>
        
        <div>
            <label for="admin_password" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-lock mr-2"></i>Password
            </label>
            <div class="relative">
                <input type="password" 
                       id="admin_password" 
                       name="admin_password" 
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12"
                       placeholder="Enter a secure password"
                       minlength="6"
                       required>
                <button type="button" 
                        onclick="togglePassword('admin_password')"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <i class="fas fa-eye text-gray-400 hover:text-gray-600" id="admin_password_icon"></i>
                </button>
            </div>
            <div class="mt-2">
                <div class="flex items-center space-x-2 text-sm">
                    <div id="length_check" class="flex items-center">
                        <i class="fas fa-times text-red-500 mr-1"></i>
                        <span class="text-red-600">At least 6 characters</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div>
            <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-lock mr-2"></i>Confirm Password
            </label>
            <div class="relative">
                <input type="password" 
                       id="confirm_password" 
                       name="confirm_password" 
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12"
                       placeholder="Confirm your password"
                       minlength="6"
                       required>
                <button type="button" 
                        onclick="togglePassword('confirm_password')"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <i class="fas fa-eye text-gray-400 hover:text-gray-600" id="confirm_password_icon"></i>
                </button>
            </div>
            <div class="mt-2">
                <div id="match_check" class="flex items-center text-sm">
                    <i class="fas fa-times text-red-500 mr-1"></i>
                    <span class="text-red-600">Passwords must match</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex">
            <i class="fas fa-exclamation-triangle text-yellow-400 mt-1 mr-3"></i>
            <div class="text-yellow-800">
                <h4 class="font-medium">Important Security Notes:</h4>
                <ul class="mt-2 text-sm list-disc list-inside">
                    <li>Use a strong, unique password for your admin account</li>
                    <li>Remember these credentials - you'll need them to log in</li>
                    <li>This account will have full system access and permissions</li>
                    <li>You can create additional user accounts after installation</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="flex justify-between">
        <a href="install_new.php?step=4" 
           class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center">
            <i class="fas fa-arrow-left mr-2"></i>
            Back
        </a>
        
        <button type="submit" 
                id="submitBtn"
                class="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center disabled:opacity-50 disabled:cursor-not-allowed">
            <i class="fas fa-rocket mr-2"></i>
            Install MyGym
        </button>
    </div>
</form>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function validatePassword() {
    const password = document.getElementById('admin_password').value;
    const confirm = document.getElementById('confirm_password').value;
    const lengthCheck = document.getElementById('length_check');
    const matchCheck = document.getElementById('match_check');
    const submitBtn = document.getElementById('submitBtn');
    
    // Check length
    if (password.length >= 6) {
        lengthCheck.innerHTML = '<i class="fas fa-check text-green-500 mr-1"></i><span class="text-green-600">At least 6 characters</span>';
    } else {
        lengthCheck.innerHTML = '<i class="fas fa-times text-red-500 mr-1"></i><span class="text-red-600">At least 6 characters</span>';
    }
    
    // Check match
    if (confirm && password === confirm) {
        matchCheck.innerHTML = '<i class="fas fa-check text-green-500 mr-1"></i><span class="text-green-600">Passwords match</span>';
    } else if (confirm) {
        matchCheck.innerHTML = '<i class="fas fa-times text-red-500 mr-1"></i><span class="text-red-600">Passwords must match</span>';
    } else {
        matchCheck.innerHTML = '<i class="fas fa-times text-red-500 mr-1"></i><span class="text-red-600">Passwords must match</span>';
    }
    
    // Enable/disable submit button
    const isValid = password.length >= 6 && password === confirm && password && confirm;
    submitBtn.disabled = !isValid;
}

// Add event listeners
document.getElementById('admin_password').addEventListener('input', validatePassword);
document.getElementById('confirm_password').addEventListener('input', validatePassword);

// Form submission
document.getElementById('adminForm').addEventListener('submit', function(e) {
    const name = document.getElementById('admin_name').value.trim();
    const email = document.getElementById('admin_email').value.trim();
    const password = document.getElementById('admin_password').value;
    const confirm = document.getElementById('confirm_password').value;
    
    if (!name || !email || !password || !confirm) {
        e.preventDefault();
        alert('Please fill in all fields.');
        return false;
    }
    
    if (password !== confirm) {
        e.preventDefault();
        alert('Passwords do not match.');
        return false;
    }
    
    if (password.length < 6) {
        e.preventDefault();
        alert('Password must be at least 6 characters long.');
        return false;
    }
    
    // Show loading state
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Installing MyGym...';
});

// Initialize validation
document.addEventListener('DOMContentLoaded', function() {
    validatePassword();
});
</script>
