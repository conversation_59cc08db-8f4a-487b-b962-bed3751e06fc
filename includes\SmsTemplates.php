<?php
/**
 * SMS Templates Class
 * MyGym Management System
 * 
 * Provides pre-built SMS templates for various gym communications
 */

require_once 'Config.php';

class SmsTemplates {
    
    /**
     * Get available SMS templates
     */
    public static function getAvailableTemplates() {
        return [
            'welcome' => [
                'name' => 'Welcome New Member',
                'description' => 'Welcome SMS for new gym members',
                'icon' => 'fas fa-user-plus',
                'color' => 'blue'
            ],
            'membership_expiry' => [
                'name' => 'Membership Expiry Reminder',
                'description' => 'Remind members about upcoming membership expiry',
                'icon' => 'fas fa-clock',
                'color' => 'orange'
            ],
            'membership_expired' => [
                'name' => 'Membership Expired',
                'description' => 'Notify members that their membership has expired',
                'icon' => 'fas fa-exclamation-triangle',
                'color' => 'red'
            ],
            'payment_reminder' => [
                'name' => 'Payment Reminder',
                'description' => 'Remind members about pending payments',
                'icon' => 'fas fa-credit-card',
                'color' => 'red'
            ],
            'payment_confirmation' => [
                'name' => 'Payment Confirmation',
                'description' => 'Confirm successful payment receipt',
                'icon' => 'fas fa-check-circle',
                'color' => 'green'
            ],
            'class_announcement' => [
                'name' => 'Class Announcement',
                'description' => 'Announce new classes or schedule changes',
                'icon' => 'fas fa-calendar',
                'color' => 'purple'
            ],
            'promotion' => [
                'name' => 'Promotion & Offers',
                'description' => 'Special offers and promotional messages',
                'icon' => 'fas fa-tags',
                'color' => 'pink'
            ],
            'maintenance' => [
                'name' => 'Maintenance Notice',
                'description' => 'Notify about gym maintenance or closures',
                'icon' => 'fas fa-tools',
                'color' => 'yellow'
            ],
            'birthday' => [
                'name' => 'Birthday Wishes',
                'description' => 'Birthday greetings for members',
                'icon' => 'fas fa-birthday-cake',
                'color' => 'indigo'
            ],
            'achievement' => [
                'name' => 'Achievement Congratulations',
                'description' => 'Congratulate members on fitness milestones',
                'icon' => 'fas fa-trophy',
                'color' => 'gold'
            ],
            'appointment_reminder' => [
                'name' => 'Appointment Reminder',
                'description' => 'Remind about personal training sessions',
                'icon' => 'fas fa-calendar-check',
                'color' => 'teal'
            ],
            'custom' => [
                'name' => 'Custom Message',
                'description' => 'Create your own custom SMS message',
                'icon' => 'fas fa-edit',
                'color' => 'gray'
            ]
        ];
    }

    /**
     * Generate SMS content based on template
     */
    public static function generateTemplate($templateType, $data = []) {
        $gymName = Config::get('gym_name') ?: 'MyGym';
        $gymPhone = Config::get('gym_phone') ?: '';
        
        // Default data
        $defaultData = [
            'member_name' => $data['member_name'] ?? 'Valued Member',
            'gym_name' => $gymName,
            'gym_phone' => $gymPhone,
            'date' => date('M j, Y'),
            'time' => date('g:i A')
        ];
        
        $data = array_merge($defaultData, $data);
        
        switch ($templateType) {
            case 'welcome':
                return self::getWelcomeTemplate($data);
            case 'membership_expiry':
                return self::getMembershipExpiryTemplate($data);
            case 'membership_expired':
                return self::getMembershipExpiredTemplate($data);
            case 'payment_reminder':
                return self::getPaymentReminderTemplate($data);
            case 'payment_confirmation':
                return self::getPaymentConfirmationTemplate($data);
            case 'class_announcement':
                return self::getClassAnnouncementTemplate($data);
            case 'promotion':
                return self::getPromotionTemplate($data);
            case 'maintenance':
                return self::getMaintenanceTemplate($data);
            case 'birthday':
                return self::getBirthdayTemplate($data);
            case 'achievement':
                return self::getAchievementTemplate($data);
            case 'appointment_reminder':
                return self::getAppointmentReminderTemplate($data);
            default:
                return self::getCustomTemplate($data);
        }
    }

    /**
     * Welcome New Member Template
     */
    private static function getWelcomeTemplate($data) {
        return "🎉 Welcome to {$data['gym_name']}, {$data['member_name']}! We're excited to have you join our fitness family. Your journey to a healthier you starts now! Need help? Call us at {$data['gym_phone']}. Let's get fit together! 💪";
    }

    /**
     * Membership Expiry Reminder Template
     */
    private static function getMembershipExpiryTemplate($data) {
        $expiryDate = $data['expiry_date'] ?? 'soon';
        return "⏰ Hi {$data['member_name']}, your {$data['gym_name']} membership expires on {$expiryDate}. Don't miss out on your fitness journey! Renew now to continue enjoying all our facilities. Call {$data['gym_phone']} or visit us today!";
    }

    /**
     * Membership Expired Template
     */
    private static function getMembershipExpiredTemplate($data) {
        $expiredDate = $data['expired_date'] ?? 'recently';
        return "❌ Hi {$data['member_name']}, your {$data['gym_name']} membership expired on {$expiredDate}. We miss you! 💔 Renew within 7 days and get 10% off! Don't let your fitness goals pause. Call {$data['gym_phone']} or visit us today. We can't wait to see you back! 💪";
    }

    /**
     * Payment Reminder Template
     */
    private static function getPaymentReminderTemplate($data) {
        $amount = $data['amount'] ?? 'your membership fee';
        $dueDate = $data['due_date'] ?? 'soon';
        return "💳 Payment reminder from {$data['gym_name']}: Hi {$data['member_name']}, your payment of {$amount} is due on {$dueDate}. Please make your payment to continue enjoying our services. Questions? Call {$data['gym_phone']}.";
    }

    /**
     * Payment Confirmation Template
     */
    private static function getPaymentConfirmationTemplate($data) {
        $amount = $data['amount'] ?? 'your payment';
        $receiptNo = $data['receipt_number'] ?? '';
        $receiptText = $receiptNo ? " Receipt: {$receiptNo}" : '';
        return "✅ Payment confirmed! Thank you {$data['member_name']} for your payment of {$amount} to {$data['gym_name']}.{$receiptText} Your membership is now active. Keep crushing those fitness goals! 💪";
    }

    /**
     * Class Announcement Template
     */
    private static function getClassAnnouncementTemplate($data) {
        $className = $data['class_name'] ?? 'New Fitness Class';
        $classTime = $data['class_time'] ?? 'TBD';
        $instructor = $data['instructor'] ?? 'our expert trainer';
        return "🏃‍♀️ New class alert at {$data['gym_name']}! Join our {$className} with {$instructor} starting {$classTime}. Limited spots available! Book now: {$data['gym_phone']}. See you there!";
    }

    /**
     * Promotion Template
     */
    private static function getPromotionTemplate($data) {
        $offer = $data['offer'] ?? 'special offer';
        $validUntil = $data['valid_until'] ?? 'limited time';
        return "🎁 Special offer from {$data['gym_name']}! {$data['member_name']}, enjoy our {$offer} - valid until {$validUntil}. Don't miss out! Call {$data['gym_phone']} or visit us today. Terms apply.";
    }

    /**
     * Maintenance Notice Template
     */
    private static function getMaintenanceTemplate($data) {
        $maintenanceDate = $data['maintenance_date'] ?? 'upcoming date';
        $duration = $data['duration'] ?? 'few hours';
        return "🔧 Maintenance notice from {$data['gym_name']}: We'll be closed on {$maintenanceDate} for {$duration} to improve our facilities. We apologize for any inconvenience. Questions? Call {$data['gym_phone']}.";
    }

    /**
     * Birthday Wishes Template
     */
    private static function getBirthdayTemplate($data) {
        return "🎂 Happy Birthday {$data['member_name']}! The entire {$data['gym_name']} family wishes you a fantastic year ahead filled with health, happiness, and fitness achievements! Celebrate with a great workout today! 🎉💪";
    }

    /**
     * Achievement Congratulations Template
     */
    private static function getAchievementTemplate($data) {
        $achievement = $data['achievement'] ?? 'fitness milestone';
        return "🏆 Congratulations {$data['member_name']}! You've achieved your {$achievement} at {$data['gym_name']}! Your dedication and hard work are truly inspiring. Keep up the amazing progress! 🌟💪";
    }

    /**
     * Appointment Reminder Template
     */
    private static function getAppointmentReminderTemplate($data) {
        $appointmentDate = $data['appointment_date'] ?? 'upcoming session';
        $trainer = $data['trainer'] ?? 'your trainer';
        return "📅 Reminder: Your personal training session with {$trainer} at {$data['gym_name']} is scheduled for {$appointmentDate}. Don't forget to bring your workout gear! Questions? Call {$data['gym_phone']}.";
    }

    /**
     * Custom Template
     */
    private static function getCustomTemplate($data) {
        $customMessage = $data['custom_message'] ?? 'Your custom message here...';
        return $customMessage;
    }

    /**
     * Get character count for SMS (160 chars for single SMS)
     */
    public static function getCharacterCount($message) {
        return strlen($message);
    }

    /**
     * Get SMS count (how many SMS messages will be sent)
     */
    public static function getSmsCount($message) {
        $length = strlen($message);
        if ($length <= 160) {
            return 1;
        } elseif ($length <= 306) {
            return 2;
        } elseif ($length <= 459) {
            return 3;
        } else {
            return ceil($length / 153); // For longer messages
        }
    }

    /**
     * Validate phone number format
     */
    public static function validatePhoneNumber($phone) {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Check if it's a valid length (10-15 digits)
        if (strlen($phone) >= 10 && strlen($phone) <= 15) {
            return $phone;
        }
        
        return false;
    }

    /**
     * Format phone number for display
     */
    public static function formatPhoneNumber($phone) {
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        if (strlen($phone) == 10) {
            return '(' . substr($phone, 0, 3) . ') ' . substr($phone, 3, 3) . '-' . substr($phone, 6);
        } elseif (strlen($phone) == 11 && substr($phone, 0, 1) == '1') {
            return '+1 (' . substr($phone, 1, 3) . ') ' . substr($phone, 4, 3) . '-' . substr($phone, 7);
        }
        
        return $phone;
    }
}
?>
