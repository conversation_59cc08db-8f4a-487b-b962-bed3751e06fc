<?php
/**
 * Profile Page
 * MyGym Management System
 */

session_start();
require_once '../config/database.php';
require_once '../includes/auth.php';

// Require authentication
Auth::requireAuth();

$db = Database::getInstance();
$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $transactionStarted = false;

    try {
        // Handle local admin profile updates
        if (Auth::isLocalAdmin()) {
            $name = trim($_POST['name'] ?? '');
            $email = trim($_POST['email'] ?? '');

            if (empty($name) || empty($email)) {
                throw new Exception('Name and email are required.');
            }

            // Update session data for local admin
            $_SESSION['user_name'] = $name;
            $_SESSION['user_email'] = $email;

            $success = 'Profile updated successfully!';

            // Get current user data after update
            $user = Auth::user();

        } else {

        // Verify CSRF token
        $csrfToken = $_POST['csrf_token'] ?? '';
        if (!Auth::verifyCsrfToken($csrfToken)) {
            throw new Exception('Invalid security token. Please try again.');
        }

        $userId = Auth::id();
        $name = trim($_POST['name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';

        // Validate required fields
        if (empty($name) || empty($email)) {
            throw new Exception('Name and email are required.');
        }

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Please enter a valid email address.');
        }

        // Check if email is already taken by another user
        $existingUser = $db->fetch("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $userId]);
        if ($existingUser) {
            throw new Exception('This email is already in use by another account.');
        }

        // Start transaction
        $db->beginTransaction();
        $transactionStarted = true;

        // Update basic profile info
        $updateResult = $db->query("UPDATE users SET name = ?, email = ? WHERE id = ?", [$name, $email, $userId]);

        // Debug: Check if update worked
        if (!$updateResult) {
            throw new Exception('Failed to update profile information.');
        }

        // Debug: Check if user exists (only for database users)
        $userExists = $db->fetch("SELECT id FROM users WHERE id = ?", [$userId]);
        if (!$userExists) {
            throw new Exception('User not found in database.');
        }

        // Handle password change if provided
        if (!empty($newPassword)) {
            // Verify current password
            $currentUser = $db->fetch("SELECT password FROM users WHERE id = ?", [$userId]);
            if (!password_verify($currentPassword, $currentUser['password'])) {
                throw new Exception('Current password is incorrect.');
            }

            // Validate new password
            if (strlen($newPassword) < 6) {
                throw new Exception('New password must be at least 6 characters long.');
            }

            if ($newPassword !== $confirmPassword) {
                throw new Exception('New password and confirmation do not match.');
            }

            // Update password
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $passwordResult = $db->query("UPDATE users SET password = ? WHERE id = ?", [$hashedPassword, $userId]);

            if (!$passwordResult) {
                throw new Exception('Failed to update password.');
            }
        }

        $db->commit();

        // Update session data to reflect changes immediately
        $_SESSION['user']['name'] = $name;
        $_SESSION['user']['email'] = $email;

        $success = 'Profile updated successfully!';

        } // End of else block for database users

    } catch (Exception $e) {
        if ($transactionStarted) {
            $db->rollback();
        }
        $error = $e->getMessage();
    }
}

// Get current user data
$user = Auth::user();

include '../includes/header.php';
?>

<div class="max-w-4xl mx-auto">
    <!-- Flash Messages -->
    <?php if ($success): ?>
        <div class="mb-6 bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-2xl p-4 shadow-lg dark:from-green-900 dark:to-green-800 dark:border-green-700">
            <div class="flex items-start">
                <div class="w-8 h-8 rounded-full bg-white dark:bg-gray-700 flex items-center justify-center mr-3 shadow-sm">
                    <i class="fas fa-check-circle text-green-500 text-sm"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-green-800 dark:text-green-200"><?= htmlspecialchars($success) ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="mb-6 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-2xl p-4 shadow-lg dark:from-red-900 dark:to-red-800 dark:border-red-700">
            <div class="flex items-start">
                <div class="w-8 h-8 rounded-full bg-white dark:bg-gray-700 flex items-center justify-center mr-3 shadow-sm">
                    <i class="fas fa-exclamation-circle text-red-500 text-sm"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-red-800 dark:text-red-200"><?= htmlspecialchars($error) ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Profile Settings</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">Manage your account information and security settings</p>

        <?php if (Auth::isLocalAdmin()): ?>
        <div class="mt-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div class="flex items-start">
                <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center mr-3">
                    <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 text-sm"></i>
                </div>
                <div class="flex-1">
                    <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200">Local Administrator Account</h4>
                    <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">
                        You can update your display name and email. Password changes require editing the configuration file.
                    </p>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Profile Card -->
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
                <div class="text-center">
                    <!-- Profile Avatar -->
                    <div class="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white text-2xl font-bold">
                            <?= strtoupper(substr($user['name'] ?? 'U', 0, 1)) ?>
                        </span>
                    </div>

                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white"><?= htmlspecialchars($user['name'] ?? 'Unknown') ?></h3>
                    <p class="text-gray-500 dark:text-gray-400 text-sm"><?= htmlspecialchars($user['email'] ?? 'No email') ?></p>
                    <p class="text-blue-600 dark:text-blue-400 text-sm font-medium capitalize mt-1">
                        <?= htmlspecialchars($user['role'] ?? 'user') ?>
                        <?php if (Auth::isLocalAdmin()): ?>
                            <span class="text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full ml-2">Local Admin</span>
                        <?php endif; ?>
                    </p>

                    <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            <p>Member since</p>
                            <p class="font-medium text-gray-900 dark:text-white">
                                <?= date('F Y', strtotime($user['created_at'] ?? 'now')) ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Form -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Account Information</h2>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mt-1">Update your personal information and password</p>
                </div>

                <form method="POST" class="p-6 space-y-6">
                    <?= Auth::csrfField() ?>

                    <!-- Basic Information -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Basic Information</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Full Name
                                </label>
                                <input type="text"
                                       id="name"
                                       name="name"
                                       value="<?= htmlspecialchars($user['name'] ?? '') ?>"
                                       required
                                       class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Email Address
                                </label>
                                <input type="email"
                                       id="email"
                                       name="email"
                                       value="<?= htmlspecialchars($user['email'] ?? '') ?>"
                                       required
                                       class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Role
                            </label>
                            <input type="text"
                                   value="<?= ucfirst(htmlspecialchars($user['role'] ?? 'user')) ?>"
                                   disabled
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-600 text-gray-500 dark:text-gray-400">
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Role cannot be changed. Contact an administrator if needed.</p>
                        </div>
                    </div>

                    <!-- Password Change -->
                    <div class="space-y-4 pt-6 border-t border-gray-200 dark:border-gray-700" <?= Auth::isLocalAdmin() ? 'style="opacity: 0.5; pointer-events: none;"' : '' ?>>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Change Password</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            <?= Auth::isLocalAdmin() ? 'Password changes require editing the configuration file' : 'Leave blank to keep current password' ?>
                        </p>

                        <div>
                            <label for="current_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Current Password
                            </label>
                            <input type="password"
                                   id="current_password"
                                   name="current_password"
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="new_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    New Password
                                </label>
                                <input type="password"
                                       id="new_password"
                                       name="new_password"
                                       class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>

                            <div>
                                <label for="confirm_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Confirm New Password
                                </label>
                                <input type="password"
                                       id="confirm_password"
                                       name="confirm_password"
                                       class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
                        <a href="../dashboard.php" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                        </a>

                        <button type="submit" class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-save mr-2"></i>Update Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Password validation
document.addEventListener('DOMContentLoaded', function() {
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    const currentPassword = document.getElementById('current_password');

    function validatePasswords() {
        if (newPassword.value || confirmPassword.value) {
            currentPassword.required = true;
            newPassword.required = true;
            confirmPassword.required = true;

            if (newPassword.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('Passwords do not match');
            } else {
                confirmPassword.setCustomValidity('');
            }
        } else {
            currentPassword.required = false;
            newPassword.required = false;
            confirmPassword.required = false;
            confirmPassword.setCustomValidity('');
        }
    }

    newPassword.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);
    currentPassword.addEventListener('input', validatePasswords);
});
</script>

<?php include '../includes/footer.php'; ?>

