<?php
/**
 * Get Assigned Members API
 * Returns all members assigned to a specific trainer
 */

session_start();

// Include required files
require_once '../config/database.php';
require_once '../includes/auth.php';

// Check authentication
if (!Auth::check()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Check permissions
if (!Auth::can('trainers.view') || !Auth::can('members.view')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Insufficient permissions']);
    exit;
}

// Set content type
header('Content-Type: application/json');

try {
    $trainerId = (int)($_GET['trainer_id'] ?? 0);
    
    if (!$trainerId) {
        echo json_encode(['success' => false, 'message' => 'Trainer ID is required']);
        exit;
    }
    
    $db = Database::getInstance();
    
    // Verify trainer exists
    $trainer = $db->fetch("SELECT id, name FROM trainers WHERE id = ?", [$trainerId]);
    if (!$trainer) {
        echo json_encode(['success' => false, 'message' => 'Trainer not found']);
        exit;
    }
    
    // Get assigned members with their details
    $members = $db->fetchAll("
        SELECT
            m.id,
            m.member_id,
            m.first_name,
            m.last_name,
            m.email,
            m.phone,
            m.avatar,
            m.status,
            m.start_date,
            m.end_date,
            m.created_at,
            p.name as plan_name,
            p.price as plan_price,
            CASE
                WHEN m.end_date < CURDATE() THEN 'expired'
                WHEN m.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 'expiring'
                ELSE m.status
            END as display_status
        FROM members m
        LEFT JOIN plans p ON m.plan_id = p.id
        WHERE m.trainer_id = ?
        ORDER BY
            CASE m.status
                WHEN 'active' THEN 1
                WHEN 'inactive' THEN 2
                ELSE 3
            END,
            m.first_name, m.last_name
    ", [$trainerId]);
    
    // Format the data for better frontend consumption
    $formattedMembers = [];
    foreach ($members as $member) {
        $formattedMembers[] = [
            'id' => (int)$member['id'],
            'member_id' => $member['member_id'],
            'first_name' => $member['first_name'],
            'last_name' => $member['last_name'],
            'email' => $member['email'],
            'phone' => $member['phone'],
            'avatar' => $member['avatar'],
            'status' => $member['status'],
            'display_status' => $member['display_status'],
            'start_date' => $member['start_date'],
            'end_date' => $member['end_date'],
            'plan_name' => $member['plan_name'],
            'plan_price' => $member['plan_price'] ? (float)$member['plan_price'] : null,
            'created_at' => $member['created_at'],
            'days_until_expiry' => $member['end_date'] ?
                max(0, (strtotime($member['end_date']) - time()) / (24 * 60 * 60)) : null
        ];
    }
    
    // Get some statistics
    $stats = [
        'total' => count($formattedMembers),
        'active' => count(array_filter($formattedMembers, fn($m) => $m['status'] === 'active')),
        'inactive' => count(array_filter($formattedMembers, fn($m) => $m['status'] === 'inactive')),
        'expired' => count(array_filter($formattedMembers, fn($m) => $m['display_status'] === 'expired')),
        'expiring' => count(array_filter($formattedMembers, fn($m) => $m['display_status'] === 'expiring'))
    ];
    
    echo json_encode([
        'success' => true,
        'trainer' => [
            'id' => (int)$trainer['id'],
            'name' => $trainer['name']
        ],
        'members' => $formattedMembers,
        'stats' => $stats,
        'total_members' => count($formattedMembers)
    ]);
    
} catch (PDOException $e) {
    error_log('Get Assigned Members Database Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred while loading assigned members. Please try again.'
    ]);
} catch (Exception $e) {
    error_log('Get Assigned Members Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to load assigned members. Please try again.'
    ]);
}
