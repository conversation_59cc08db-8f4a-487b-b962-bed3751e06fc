<!-- Step 4: Site Settings -->
<div class="text-center mb-6">
    <i class="fas fa-cog text-4xl text-blue-600 mb-4"></i>
    <h2 class="text-2xl font-bold text-gray-900 mb-2">Site Configuration</h2>
    <p class="text-gray-600">Configure your gym's basic settings</p>
</div>

<form method="POST" class="space-y-6">
    <input type="hidden" name="step" value="4">
    
    <div class="space-y-6">
        <div>
            <label for="gym_name" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-dumbbell mr-2"></i>Gym Name
            </label>
            <input type="text" 
                   id="gym_name" 
                   name="gym_name" 
                   value="<?= htmlspecialchars($_POST['gym_name'] ?? '') ?>"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                   placeholder="e.g., FitLife Gym, PowerHouse Fitness"
                   required>
            <p class="mt-1 text-sm text-gray-500">This will appear throughout the system and on receipts</p>
        </div>
        
        <div>
            <label for="site_url" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-globe mr-2"></i>Site URL
            </label>
            <input type="url" 
                   id="site_url" 
                   name="site_url" 
                   value="<?= htmlspecialchars($_POST['site_url'] ?? 'http://' . ($_SERVER['HTTP_HOST'] ?? 'localhost')) ?>"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                   placeholder="https://yourgym.com"
                   required>
            <p class="mt-1 text-sm text-gray-500">The full URL where your gym system will be accessible</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-dollar-sign mr-2"></i>Currency
                </label>
                <select id="currency" 
                        name="currency" 
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="USD" <?= ($_POST['currency'] ?? 'USD') === 'USD' ? 'selected' : '' ?>>USD - US Dollar</option>
                    <option value="EUR" <?= ($_POST['currency'] ?? '') === 'EUR' ? 'selected' : '' ?>>EUR - Euro</option>
                    <option value="GBP" <?= ($_POST['currency'] ?? '') === 'GBP' ? 'selected' : '' ?>>GBP - British Pound</option>
                    <option value="CAD" <?= ($_POST['currency'] ?? '') === 'CAD' ? 'selected' : '' ?>>CAD - Canadian Dollar</option>
                    <option value="AUD" <?= ($_POST['currency'] ?? '') === 'AUD' ? 'selected' : '' ?>>AUD - Australian Dollar</option>
                    <option value="JPY" <?= ($_POST['currency'] ?? '') === 'JPY' ? 'selected' : '' ?>>JPY - Japanese Yen</option>
                    <option value="INR" <?= ($_POST['currency'] ?? '') === 'INR' ? 'selected' : '' ?>>INR - Indian Rupee</option>
                    <option value="BRL" <?= ($_POST['currency'] ?? '') === 'BRL' ? 'selected' : '' ?>>BRL - Brazilian Real</option>
                    <option value="MXN" <?= ($_POST['currency'] ?? '') === 'MXN' ? 'selected' : '' ?>>MXN - Mexican Peso</option>
                    <option value="ZAR" <?= ($_POST['currency'] ?? '') === 'ZAR' ? 'selected' : '' ?>>ZAR - South African Rand</option>
                </select>
                <p class="mt-1 text-sm text-gray-500">Currency for payments and pricing</p>
            </div>
            
            <div>
                <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-clock mr-2"></i>Timezone
                </label>
                <select id="timezone" 
                        name="timezone" 
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="UTC" <?= ($_POST['timezone'] ?? 'UTC') === 'UTC' ? 'selected' : '' ?>>UTC - Coordinated Universal Time</option>
                    <option value="America/New_York" <?= ($_POST['timezone'] ?? '') === 'America/New_York' ? 'selected' : '' ?>>Eastern Time (US & Canada)</option>
                    <option value="America/Chicago" <?= ($_POST['timezone'] ?? '') === 'America/Chicago' ? 'selected' : '' ?>>Central Time (US & Canada)</option>
                    <option value="America/Denver" <?= ($_POST['timezone'] ?? '') === 'America/Denver' ? 'selected' : '' ?>>Mountain Time (US & Canada)</option>
                    <option value="America/Los_Angeles" <?= ($_POST['timezone'] ?? '') === 'America/Los_Angeles' ? 'selected' : '' ?>>Pacific Time (US & Canada)</option>
                    <option value="Europe/London" <?= ($_POST['timezone'] ?? '') === 'Europe/London' ? 'selected' : '' ?>>London</option>
                    <option value="Europe/Paris" <?= ($_POST['timezone'] ?? '') === 'Europe/Paris' ? 'selected' : '' ?>>Paris</option>
                    <option value="Europe/Berlin" <?= ($_POST['timezone'] ?? '') === 'Europe/Berlin' ? 'selected' : '' ?>>Berlin</option>
                    <option value="Asia/Tokyo" <?= ($_POST['timezone'] ?? '') === 'Asia/Tokyo' ? 'selected' : '' ?>>Tokyo</option>
                    <option value="Asia/Shanghai" <?= ($_POST['timezone'] ?? '') === 'Asia/Shanghai' ? 'selected' : '' ?>>Shanghai</option>
                    <option value="Asia/Kolkata" <?= ($_POST['timezone'] ?? '') === 'Asia/Kolkata' ? 'selected' : '' ?>>Mumbai</option>
                    <option value="Australia/Sydney" <?= ($_POST['timezone'] ?? '') === 'Australia/Sydney' ? 'selected' : '' ?>>Sydney</option>
                </select>
                <p class="mt-1 text-sm text-gray-500">Your local timezone for dates and times</p>
            </div>
        </div>
    </div>
    
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex">
            <i class="fas fa-lightbulb text-green-400 mt-1 mr-3"></i>
            <div class="text-green-800">
                <h4 class="font-medium">Configuration Tips:</h4>
                <ul class="mt-2 text-sm list-disc list-inside">
                    <li>Choose a memorable gym name that represents your brand</li>
                    <li>Use your actual domain name for the site URL</li>
                    <li>Select the currency you'll be using for membership fees</li>
                    <li>Choose your local timezone for accurate scheduling</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="flex justify-between">
        <a href="install_new.php?step=3" 
           class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center">
            <i class="fas fa-arrow-left mr-2"></i>
            Back
        </a>
        
        <button type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center">
            Continue
            <i class="fas fa-arrow-right ml-2"></i>
        </button>
    </div>
</form>

<script>
// Auto-detect site URL
document.addEventListener('DOMContentLoaded', function() {
    const siteUrlInput = document.getElementById('site_url');
    if (!siteUrlInput.value || siteUrlInput.value === 'http://localhost') {
        const protocol = window.location.protocol;
        const host = window.location.host;
        const path = window.location.pathname.replace('/install_new.php', '');
        siteUrlInput.value = protocol + '//' + host + path;
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const gymName = document.getElementById('gym_name').value.trim();
    const siteUrl = document.getElementById('site_url').value.trim();
    
    if (!gymName || !siteUrl) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return false;
    }
    
    // Validate URL format
    try {
        new URL(siteUrl);
    } catch (e) {
        e.preventDefault();
        alert('Please enter a valid site URL (e.g., https://yourgym.com)');
        return false;
    }
});
</script>
