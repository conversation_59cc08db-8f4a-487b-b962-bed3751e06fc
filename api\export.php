<?php
/**
 * Export API
 * MyGym Management System
 */

session_start();

// Include required files
require_once '../config/database.php';
require_once '../includes/auth.php';

// Check authentication
if (!isset($_SESSION['user_id']) && !isset($_SESSION['local_admin_username'])) {
    http_response_code(401);
    exit('Unauthorized');
}

// Get parameters
$type = $_GET['type'] ?? '';
$format = $_GET['format'] ?? 'csv';

if (!$type) {
    http_response_code(400);
    exit('Export type is required');
}

try {
    // Get database instance
    $db = Database::getInstance();
    
    // Get date filters if provided
    $dateFrom = $_GET['date_from'] ?? date('Y-m-01');
    $dateTo = $_GET['date_to'] ?? date('Y-m-d');
    
    switch ($type) {
        case 'members':
            exportMembers($db, $format);
            break;
        case 'payments':
            exportPayments($db, $format, $dateFrom, $dateTo);
            break;
        case 'reports':
            exportReports($db, $format, $dateFrom, $dateTo);
            break;
        default:
            http_response_code(400);
            exit('Invalid export type');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    exit('Export error: ' . $e->getMessage());
}

function exportMembers($db, $format) {
    $data = $db->fetchAll("
        SELECT
            m.member_id,
            CONCAT(m.first_name, ' ', m.last_name) as full_name,
            m.email,
            m.phone,
            m.date_of_birth,
            m.gender,
            m.status,
            p.name as plan_name,
            m.start_date,
            m.end_date,
            m.created_at
        FROM members m
        LEFT JOIN plans p ON m.plan_id = p.id
        ORDER BY m.created_at DESC
    ");
    
    $filename = 'members_export_' . date('Y-m-d');
    
    if ($format === 'csv') {
        exportCSV($data, $filename, [
            'member_id' => 'Member ID',
            'full_name' => 'Full Name',
            'email' => 'Email',
            'phone' => 'Phone',
            'date_of_birth' => 'Date of Birth',
            'gender' => 'Gender',
            'status' => 'Status',
            'plan_name' => 'Plan',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'created_at' => 'Created At'
        ]);
    } else {
        exportExcel($data, $filename, 'Members Export');
    }
}

function exportPayments($db, $format, $dateFrom, $dateTo) {
    $data = $db->fetchAll("
        SELECT
            p.receipt_number,
            CONCAT(m.first_name, ' ', m.last_name) as member_name,
            m.member_id,
            pl.name as plan_name,
            p.amount,
            p.payment_method,
            p.payment_date,
            p.start_date,
            p.end_date,
            u.name as processed_by
        FROM payments p
        JOIN members m ON p.member_id = m.id
        LEFT JOIN plans pl ON p.plan_id = pl.id
        LEFT JOIN users u ON p.processed_by = u.id
        WHERE p.payment_date BETWEEN ? AND ?
        ORDER BY p.payment_date DESC
    ", [$dateFrom, $dateTo]);
    
    $filename = 'payments_export_' . date('Y-m-d');
    
    if ($format === 'csv') {
        exportCSV($data, $filename, [
            'receipt_number' => 'Receipt Number',
            'member_name' => 'Member Name',
            'member_id' => 'Member ID',
            'plan_name' => 'Plan',
            'amount' => 'Amount',
            'payment_method' => 'Payment Method',
            'payment_date' => 'Payment Date',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'processed_by' => 'Processed By'
        ]);
    } else {
        exportExcel($data, $filename, 'Payments Export');
    }
}

function exportReports($db, $format, $dateFrom, $dateTo) {
    // Get comprehensive report data
    $reportData = [];

    // Revenue summary
    $revenueData = $db->fetchAll("
        SELECT
            DATE(payment_date) as date,
            COUNT(*) as payment_count,
            SUM(amount) as total_amount
        FROM payments
        WHERE payment_date BETWEEN ? AND ?
        GROUP BY DATE(payment_date)
        ORDER BY payment_date ASC
    ", [$dateFrom, $dateTo]);

    // Member statistics
    $memberStats = $db->fetch("
        SELECT
            COUNT(*) as total_members,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_members,
            SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired_members,
            SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as new_members
        FROM members
    ", [$dateFrom]);

    // Plan performance
    $planData = $db->fetchAll("
        SELECT
            p.name,
            COUNT(m.id) as member_count,
            COALESCE(SUM(py.amount), 0) as revenue
        FROM plans p
        LEFT JOIN members m ON p.id = m.plan_id AND m.status = 'active'
        LEFT JOIN payments py ON p.id = py.plan_id AND py.payment_date BETWEEN ? AND ?
        WHERE p.is_active = 1
        GROUP BY p.id, p.name
        ORDER BY revenue DESC
    ", [$dateFrom, $dateTo]);
    
    $filename = 'reports_export_' . date('Y-m-d');
    
    if ($format === 'pdf') {
        exportReportsPDF($revenueData, $memberStats, $planData, $filename, $dateFrom, $dateTo);
    } else {
        exportReportsExcel($revenueData, $memberStats, $planData, $filename, $dateFrom, $dateTo);
    }
}

function exportCSV($data, $filename, $headers) {
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    header('Cache-Control: private, max-age=0, must-revalidate');
    header('Pragma: public');
    
    $output = fopen('php://output', 'w');
    
    // Write headers
    fputcsv($output, array_values($headers));
    
    // Write data
    foreach ($data as $row) {
        $csvRow = [];
        foreach (array_keys($headers) as $key) {
            $csvRow[] = $row[$key] ?? '';
        }
        fputcsv($output, $csvRow);
    }
    
    fclose($output);
}

function exportExcel($data, $filename, $title) {
    // Simple HTML table that Excel can open
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    header('Cache-Control: private, max-age=0, must-revalidate');
    header('Pragma: public');
    
    echo '<html><head><meta charset="UTF-8"><title>' . $title . '</title></head><body>';
    echo '<h1>' . $title . '</h1>';
    echo '<table border="1">';
    
    if (!empty($data)) {
        // Headers
        echo '<tr>';
        foreach (array_keys($data[0]) as $header) {
            echo '<th>' . htmlspecialchars(ucwords(str_replace('_', ' ', $header))) . '</th>';
        }
        echo '</tr>';
        
        // Data
        foreach ($data as $row) {
            echo '<tr>';
            foreach ($row as $cell) {
                echo '<td>' . htmlspecialchars($cell ?? '') . '</td>';
            }
            echo '</tr>';
        }
    }
    
    echo '</table></body></html>';
}

function exportReportsPDF($revenueData, $memberStats, $planData, $filename, $dateFrom, $dateTo) {
    // Generate HTML report that can be saved as PDF
    header('Content-Type: text/html');
    header('Content-Disposition: attachment; filename="' . $filename . '.html"');
    
    echo generateReportsHTML($revenueData, $memberStats, $planData, $dateFrom, $dateTo);
}

function exportReportsExcel($revenueData, $memberStats, $planData, $filename, $dateFrom, $dateTo) {
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    
    echo generateReportsExcel($revenueData, $memberStats, $planData, $dateFrom, $dateTo);
}

function generateReportsHTML($revenueData, $memberStats, $planData, $dateFrom, $dateTo) {
    $totalRevenue = array_sum(array_column($revenueData, 'total_amount'));
    $totalPayments = array_sum(array_column($revenueData, 'payment_count'));
    $avgDailyRevenue = count($revenueData) > 0 ? $totalRevenue / count($revenueData) : 0;

    // Get additional data for comprehensive report
    $db = Database::getInstance();

    // Payment methods breakdown
    $paymentMethods = $db->fetchAll("
        SELECT
            payment_method,
            COUNT(*) as transaction_count,
            SUM(amount) as total_amount,
            AVG(amount) as avg_amount
        FROM payments
        WHERE payment_date BETWEEN ? AND ?
        GROUP BY payment_method
        ORDER BY total_amount DESC
    ", [$dateFrom, $dateTo]);

    // Hourly payment distribution
    $hourlyData = $db->fetchAll("
        SELECT
            HOUR(created_at) as hour,
            COUNT(*) as payment_count,
            SUM(amount) as total_amount
        FROM payments
        WHERE payment_date BETWEEN ? AND ?
        GROUP BY HOUR(created_at)
        ORDER BY hour
    ", [$dateFrom, $dateTo]);

    // Top performing days
    $topDays = $db->fetchAll("
        SELECT
            DATE(payment_date) as date,
            DAYNAME(payment_date) as day_name,
            COUNT(*) as payment_count,
            SUM(amount) as total_amount
        FROM payments
        WHERE payment_date BETWEEN ? AND ?
        GROUP BY DATE(payment_date)
        ORDER BY total_amount DESC
        LIMIT 5
    ", [$dateFrom, $dateTo]);

    // Recent payments with details
    $recentPayments = $db->fetchAll("
        SELECT
            p.receipt_number,
            p.amount,
            p.payment_method,
            p.payment_date,
            p.created_at,
            CONCAT(m.first_name, ' ', m.last_name) as member_name,
            pl.name as plan_name
        FROM payments p
        LEFT JOIN members m ON p.member_id = m.id
        LEFT JOIN plans pl ON p.plan_id = pl.id
        WHERE p.payment_date BETWEEN ? AND ?
        ORDER BY p.created_at DESC
        LIMIT 20
    ", [$dateFrom, $dateTo]);

    return '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>MyGym Comprehensive Business Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #007cba;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #007cba;
            font-size: 28px;
            margin: 0;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .summary-item {
            text-align: center;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        .summary-item h3 {
            color: #007cba;
            margin: 0 0 10px 0;
            font-size: 14px;
            text-transform: uppercase;
        }
        .summary-item .value {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin: 40px 0 15px 0;
            color: #007cba;
            border-left: 4px solid #007cba;
            padding-left: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #007cba 0%, #0056b3 100%);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 12px;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #e3f2fd;
        }
        .chart-placeholder {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 2px dashed #007cba;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            color: #007cba;
            font-weight: bold;
        }
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏋️ MyGym Comprehensive Business Report</h1>
            <p><strong>Report Period:</strong> ' . date('F j, Y', strtotime($dateFrom)) . ' - ' . date('F j, Y', strtotime($dateTo)) . '</p>
            <p><strong>Generated:</strong> ' . date('F j, Y \a\t g:i A T') . '</p>
            <p><strong>Report Duration:</strong> ' . (strtotime($dateTo) - strtotime($dateFrom)) / (60*60*24) . ' days</p>
        </div>

        <div class="summary">
            <div class="summary-item">
                <h3>💰 Total Revenue</h3>
                <div class="value">$' . number_format($totalRevenue, 2) . '</div>
            </div>
            <div class="summary-item">
                <h3>📊 Total Payments</h3>
                <div class="value">' . number_format($totalPayments) . '</div>
            </div>
            <div class="summary-item">
                <h3>👥 Active Members</h3>
                <div class="value">' . number_format($memberStats['active_members']) . '</div>
            </div>
            <div class="summary-item">
                <h3>📈 Avg Daily Revenue</h3>
                <div class="value">$' . number_format($avgDailyRevenue, 2) . '</div>
            </div>
            <div class="summary-item">
                <h3>📋 Total Members</h3>
                <div class="value">' . number_format($memberStats['total_members']) . '</div>
            </div>
            <div class="summary-item">
                <h3>⏰ Expired Members</h3>
                <div class="value">' . number_format($memberStats['expired_members']) . '</div>
            </div>
        </div>

        <div class="chart-placeholder">
            📊 Revenue Chart Placeholder<br>
            <small>(Daily revenue trend from ' . date('M j', strtotime($dateFrom)) . ' to ' . date('M j', strtotime($dateTo)) . ')</small>
        </div>

        <div class="section-title">📅 Daily Revenue Breakdown</div>
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Day of Week</th>
                    <th>Payments Count</th>
                    <th>Total Revenue</th>
                    <th>Avg per Payment</th>
                </tr>
            </thead>
            <tbody>';

    foreach ($revenueData as $day) {
        $avgPerPayment = $day['payment_count'] > 0 ? $day['total_amount'] / $day['payment_count'] : 0;
        echo '<tr>
            <td>' . date('F j, Y', strtotime($day['date'])) . '</td>
            <td>' . date('l', strtotime($day['date'])) . '</td>
            <td>' . number_format($day['payment_count']) . '</td>
            <td>$' . number_format($day['total_amount'], 2) . '</td>
            <td>$' . number_format($avgPerPayment, 2) . '</td>
        </tr>';
    }

    echo '</tbody>
        </table>';

    if (!empty($topDays)) {
        echo '<div class="section-title">🏆 Top Performing Days</div>
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Day of Week</th>
                    <th>Payments</th>
                    <th>Revenue</th>
                </tr>
            </thead>
            <tbody>';

        foreach ($topDays as $day) {
            echo '<tr>
                <td>' . date('F j, Y', strtotime($day['date'])) . '</td>
                <td>' . $day['day_name'] . '</td>
                <td>' . number_format($day['payment_count']) . '</td>
                <td>$' . number_format($day['total_amount'], 2) . '</td>
            </tr>';
        }

        echo '</tbody>
        </table>';
    }

    echo '<div class="grid-2">
            <div>
                <div class="section-title">💳 Payment Methods</div>
                <table>
                    <thead>
                        <tr>
                            <th>Method</th>
                            <th>Count</th>
                            <th>Revenue</th>
                            <th>Avg Amount</th>
                        </tr>
                    </thead>
                    <tbody>';

    foreach ($paymentMethods as $method) {
        echo '<tr>
            <td>' . ucfirst(str_replace('_', ' ', $method['payment_method'])) . '</td>
            <td>' . number_format($method['transaction_count']) . '</td>
            <td>$' . number_format($method['total_amount'], 2) . '</td>
            <td>$' . number_format($method['avg_amount'], 2) . '</td>
        </tr>';
    }

    echo '</tbody>
                </table>
            </div>

            <div>
                <div class="section-title">🏋️ Plan Performance</div>
                <table>
                    <thead>
                        <tr>
                            <th>Plan Name</th>
                            <th>Members</th>
                            <th>Revenue</th>
                        </tr>
                    </thead>
                    <tbody>';

    foreach ($planData as $plan) {
        echo '<tr>
            <td>' . htmlspecialchars($plan['name']) . '</td>
            <td>' . number_format($plan['member_count']) . '</td>
            <td>$' . number_format($plan['revenue'], 2) . '</td>
        </tr>';
    }

    echo '</tbody>
                </table>
            </div>
        </div>';

    if (!empty($hourlyData)) {
        echo '<div class="section-title">🕐 Hourly Payment Distribution</div>
        <table>
            <thead>
                <tr>
                    <th>Hour</th>
                    <th>Payments</th>
                    <th>Revenue</th>
                </tr>
            </thead>
            <tbody>';

        foreach ($hourlyData as $hour) {
            $timeFormat = date('g:i A', mktime($hour['hour'], 0, 0));
            echo '<tr>
                <td>' . $timeFormat . '</td>
                <td>' . number_format($hour['payment_count']) . '</td>
                <td>$' . number_format($hour['total_amount'], 2) . '</td>
            </tr>';
        }

        echo '</tbody>
        </table>';
    }

    if (!empty($recentPayments)) {
        echo '<div class="section-title">🕒 Recent Payments (Last 20)</div>
        <table>
            <thead>
                <tr>
                    <th>Receipt #</th>
                    <th>Member</th>
                    <th>Plan</th>
                    <th>Amount</th>
                    <th>Method</th>
                    <th>Date & Time</th>
                </tr>
            </thead>
            <tbody>';

        foreach ($recentPayments as $payment) {
            echo '<tr>
                <td>' . htmlspecialchars($payment['receipt_number']) . '</td>
                <td>' . htmlspecialchars($payment['member_name']) . '</td>
                <td>' . htmlspecialchars($payment['plan_name'] ?? 'N/A') . '</td>
                <td>$' . number_format($payment['amount'], 2) . '</td>
                <td>' . ucfirst(str_replace('_', ' ', $payment['payment_method'])) . '</td>
                <td>' . date('M j, Y g:i A', strtotime($payment['created_at'])) . '</td>
            </tr>';
        }

        echo '</tbody>
        </table>';
    }

    echo '<div class="footer">
            <p>This report was automatically generated by MyGym Management System</p>
            <p>For questions about this report, please contact your system administrator</p>
        </div>
    </div>
</body>
</html>';
}

function generateReportsExcel($revenueData, $memberStats, $planData, $dateFrom, $dateTo) {
    $totalRevenue = array_sum(array_column($revenueData, 'total_amount'));
    $totalPayments = array_sum(array_column($revenueData, 'payment_count'));
    $avgDailyRevenue = count($revenueData) > 0 ? $totalRevenue / count($revenueData) : 0;

    // Get additional data for comprehensive Excel report
    $db = Database::getInstance();

    // Payment methods breakdown
    $paymentMethods = $db->fetchAll("
        SELECT
            payment_method,
            COUNT(*) as transaction_count,
            SUM(amount) as total_amount,
            AVG(amount) as avg_amount
        FROM payments
        WHERE payment_date BETWEEN ? AND ?
        GROUP BY payment_method
        ORDER BY total_amount DESC
    ", [$dateFrom, $dateTo]);

    // Hourly payment distribution
    $hourlyData = $db->fetchAll("
        SELECT
            HOUR(created_at) as hour,
            COUNT(*) as payment_count,
            SUM(amount) as total_amount
        FROM payments
        WHERE payment_date BETWEEN ? AND ?
        GROUP BY HOUR(created_at)
        ORDER BY hour
    ", [$dateFrom, $dateTo]);

    // All payments with detailed information
    $allPayments = $db->fetchAll("
        SELECT
            p.receipt_number,
            p.amount,
            p.payment_method,
            p.payment_date,
            p.created_at,
            CONCAT(m.first_name, ' ', m.last_name) as member_name,
            m.member_id,
            pl.name as plan_name,
            pl.duration_months,
            u.name as processed_by
        FROM payments p
        LEFT JOIN members m ON p.member_id = m.id
        LEFT JOIN plans pl ON p.plan_id = pl.id
        LEFT JOIN users u ON p.processed_by = u.id
        WHERE p.payment_date BETWEEN ? AND ?
        ORDER BY p.created_at DESC
    ", [$dateFrom, $dateTo]);

    $html = '<html><head><meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; }
        h1 { color: #007cba; font-size: 24px; }
        h2 { color: #007cba; font-size: 18px; margin-top: 30px; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th { background-color: #007cba; color: white; padding: 10px; text-align: left; font-weight: bold; }
        td { padding: 8px; border: 1px solid #ddd; }
        tr:nth-child(even) { background-color: #f2f2f2; }
        .summary-table th { background-color: #28a745; }
        .highlight { background-color: #fff3cd; font-weight: bold; }
    </style>
    </head><body>';

    $html .= '<h1>🏋️ MyGym Comprehensive Business Report</h1>';
    $html .= '<p><strong>Report Period:</strong> ' . date('F j, Y', strtotime($dateFrom)) . ' - ' . date('F j, Y', strtotime($dateTo)) . '</p>';
    $html .= '<p><strong>Generated:</strong> ' . date('F j, Y \a\t g:i A T') . '</p>';
    $html .= '<p><strong>Report Duration:</strong> ' . (strtotime($dateTo) - strtotime($dateFrom)) / (60*60*24) . ' days</p><br>';

    // Executive Summary
    $html .= '<h2>📊 Executive Summary</h2>';
    $html .= '<table class="summary-table">';
    $html .= '<tr><th>Metric</th><th>Value</th><th>Details</th></tr>';
    $html .= '<tr><td>Total Revenue</td><td class="highlight">$' . number_format($totalRevenue, 2) . '</td><td>All payments in period</td></tr>';
    $html .= '<tr><td>Total Payments</td><td class="highlight">' . number_format($totalPayments) . '</td><td>Number of transactions</td></tr>';
    $html .= '<tr><td>Average Daily Revenue</td><td class="highlight">$' . number_format($avgDailyRevenue, 2) . '</td><td>Revenue per day</td></tr>';
    $html .= '<tr><td>Active Members</td><td class="highlight">' . number_format($memberStats['active_members']) . '</td><td>Currently active</td></tr>';
    $html .= '<tr><td>Total Members</td><td class="highlight">' . number_format($memberStats['total_members']) . '</td><td>All members in system</td></tr>';
    $html .= '<tr><td>Expired Members</td><td class="highlight">' . number_format($memberStats['expired_members']) . '</td><td>Need renewal</td></tr>';
    $html .= '</table><br>';

    // Daily Revenue Breakdown
    $html .= '<h2>📅 Daily Revenue Breakdown</h2>';
    $html .= '<table>';
    $html .= '<tr><th>Date</th><th>Day of Week</th><th>Payments Count</th><th>Total Revenue</th><th>Avg per Payment</th></tr>';

    foreach ($revenueData as $day) {
        $avgPerPayment = $day['payment_count'] > 0 ? $day['total_amount'] / $day['payment_count'] : 0;
        $html .= '<tr>';
        $html .= '<td>' . date('F j, Y', strtotime($day['date'])) . '</td>';
        $html .= '<td>' . date('l', strtotime($day['date'])) . '</td>';
        $html .= '<td>' . number_format($day['payment_count']) . '</td>';
        $html .= '<td>$' . number_format($day['total_amount'], 2) . '</td>';
        $html .= '<td>$' . number_format($avgPerPayment, 2) . '</td>';
        $html .= '</tr>';
    }

    $html .= '</table><br>';

    // Payment Methods Analysis
    if (!empty($paymentMethods)) {
        $html .= '<h2>💳 Payment Methods Analysis</h2>';
        $html .= '<table>';
        $html .= '<tr><th>Payment Method</th><th>Transaction Count</th><th>Total Revenue</th><th>Average Amount</th><th>% of Total Revenue</th></tr>';

        foreach ($paymentMethods as $method) {
            $percentage = $totalRevenue > 0 ? ($method['total_amount'] / $totalRevenue) * 100 : 0;
            $html .= '<tr>';
            $html .= '<td>' . ucfirst(str_replace('_', ' ', $method['payment_method'])) . '</td>';
            $html .= '<td>' . number_format($method['transaction_count']) . '</td>';
            $html .= '<td>$' . number_format($method['total_amount'], 2) . '</td>';
            $html .= '<td>$' . number_format($method['avg_amount'], 2) . '</td>';
            $html .= '<td>' . number_format($percentage, 1) . '%</td>';
            $html .= '</tr>';
        }

        $html .= '</table><br>';
    }

    // Plan Performance
    $html .= '<h2>🏋️ Plan Performance Analysis</h2>';
    $html .= '<table>';
    $html .= '<tr><th>Plan Name</th><th>Active Members</th><th>Revenue Generated</th><th>% of Total Revenue</th></tr>';

    foreach ($planData as $plan) {
        $percentage = $totalRevenue > 0 ? ($plan['revenue'] / $totalRevenue) * 100 : 0;
        $html .= '<tr>';
        $html .= '<td>' . htmlspecialchars($plan['name']) . '</td>';
        $html .= '<td>' . number_format($plan['member_count']) . '</td>';
        $html .= '<td>$' . number_format($plan['revenue'], 2) . '</td>';
        $html .= '<td>' . number_format($percentage, 1) . '%</td>';
        $html .= '</tr>';
    }

    $html .= '</table><br>';

    // Hourly Distribution
    if (!empty($hourlyData)) {
        $html .= '<h2>🕐 Hourly Payment Distribution</h2>';
        $html .= '<table>';
        $html .= '<tr><th>Hour</th><th>Payment Count</th><th>Revenue</th><th>% of Daily Total</th></tr>';

        foreach ($hourlyData as $hour) {
            $percentage = $totalRevenue > 0 ? ($hour['total_amount'] / $totalRevenue) * 100 : 0;
            $timeFormat = date('g:i A', mktime($hour['hour'], 0, 0));
            $html .= '<tr>';
            $html .= '<td>' . $timeFormat . '</td>';
            $html .= '<td>' . number_format($hour['payment_count']) . '</td>';
            $html .= '<td>$' . number_format($hour['total_amount'], 2) . '</td>';
            $html .= '<td>' . number_format($percentage, 1) . '%</td>';
            $html .= '</tr>';
        }

        $html .= '</table><br>';
    }

    // Detailed Payment Records
    if (!empty($allPayments)) {
        $html .= '<h2>🕒 Detailed Payment Records</h2>';
        $html .= '<table>';
        $html .= '<tr><th>Receipt #</th><th>Member Name</th><th>Member ID</th><th>Plan</th><th>Duration</th><th>Amount</th><th>Payment Method</th><th>Payment Date</th><th>Time</th><th>Processed By</th></tr>';

        foreach ($allPayments as $payment) {
            $html .= '<tr>';
            $html .= '<td>' . htmlspecialchars($payment['receipt_number']) . '</td>';
            $html .= '<td>' . htmlspecialchars($payment['member_name']) . '</td>';
            $html .= '<td>' . htmlspecialchars($payment['member_id']) . '</td>';
            $html .= '<td>' . htmlspecialchars($payment['plan_name'] ?? 'N/A') . '</td>';
            $html .= '<td>' . ($payment['duration_months'] ? $payment['duration_months'] . ' months' : 'N/A') . '</td>';
            $html .= '<td>$' . number_format($payment['amount'], 2) . '</td>';
            $html .= '<td>' . ucfirst(str_replace('_', ' ', $payment['payment_method'])) . '</td>';
            $html .= '<td>' . date('M j, Y', strtotime($payment['payment_date'])) . '</td>';
            $html .= '<td>' . date('g:i A', strtotime($payment['created_at'])) . '</td>';
            $html .= '<td>' . htmlspecialchars($payment['processed_by'] ?? 'System') . '</td>';
            $html .= '</tr>';
        }

        $html .= '</table><br>';
    }

    $html .= '<br><hr><p style="text-align: center; color: #666; font-size: 12px;">';
    $html .= 'This report was automatically generated by MyGym Management System<br>';
    $html .= 'Generated on: ' . date('F j, Y \a\t g:i A T') . '<br>';
    $html .= 'For questions about this report, please contact your system administrator';
    $html .= '</p>';

    $html .= '</body></html>';

    return $html;
}
?>
