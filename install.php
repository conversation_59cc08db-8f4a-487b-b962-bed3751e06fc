<?php
/**
 * MyGym Management System Installation Wizard
 * Version 2.0 - Complete rewrite with 5-step process
 * Includes EULA and guaranteed working authentication
 */

session_start();

// Redirect if already installed
if (file_exists('config/config.php')) {
    header('Location: index.php');
    exit;
}

// Initialize installation session
if (!isset($_SESSION['install_session'])) {
    $_SESSION['install_session'] = [
        'started' => time(),
        'step1_eula' => false,
        'step2_requirements' => false,
        'step3_database' => false,
        'step4_settings' => false,
        'step5_admin' => false,
        'data' => []
    ];
}

$currentStep = (int)($_GET['step'] ?? $_POST['step'] ?? 1);
$errors = [];
$success = false;
$installSession = &$_SESSION['install_session'];

// Validate step access
if ($currentStep > 1 && !$installSession['step1_eula']) {
    $currentStep = 1;
}
if ($currentStep > 2 && !$installSession['step2_requirements']) {
    $currentStep = 2;
}
if ($currentStep > 3 && !$installSession['step3_database']) {
    $currentStep = 3;
}
if ($currentStep > 4 && !$installSession['step4_settings']) {
    $currentStep = 4;
}

// Process form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($currentStep) {
        case 1:
            $result = processEulaStep();
            break;
        case 2:
            $result = processRequirementsStep();
            break;
        case 3:
            $result = processDatabaseStep();
            break;
        case 4:
            $result = processSettingsStep();
            break;
        case 5:
            $result = processAdminStep();
            break;
    }
    
    if (isset($result)) {
        if ($result['success']) {
            $success = true;
            if (isset($result['next_step'])) {
                $currentStep = $result['next_step'];
            }
        } else {
            $errors = $result['errors'] ?? ['An error occurred'];
        }
    }
}

/**
 * Step 1: EULA Agreement
 */
function processEulaStep() {
    global $installSession;
    
    $agreed = isset($_POST['agree_eula']) && $_POST['agree_eula'] === '1';
    
    if (!$agreed) {
        return ['success' => false, 'errors' => ['You must agree to the End User License Agreement to continue.']];
    }
    
    $installSession['step1_eula'] = true;
    return ['success' => true, 'next_step' => 2];
}

/**
 * Step 2: Requirements Check
 */
function processRequirementsStep() {
    global $installSession;
    
    // Check PHP version
    if (version_compare(PHP_VERSION, '7.4.0', '<')) {
        return ['success' => false, 'errors' => ['PHP 7.4 or higher is required. Current version: ' . PHP_VERSION]];
    }
    
    // Check required extensions
    $requiredExtensions = ['pdo', 'pdo_mysql', 'curl', 'json', 'mbstring'];
    $missingExtensions = [];
    
    foreach ($requiredExtensions as $ext) {
        if (!extension_loaded($ext)) {
            $missingExtensions[] = $ext;
        }
    }
    
    if (!empty($missingExtensions)) {
        return ['success' => false, 'errors' => ['Missing PHP extensions: ' . implode(', ', $missingExtensions)]];
    }
    
    // Check directory permissions
    $directories = ['config', 'uploads', 'backups'];
    $permissionErrors = [];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                $permissionErrors[] = "Cannot create directory: {$dir}";
            }
        } elseif (!is_writable($dir)) {
            $permissionErrors[] = "Directory not writable: {$dir}";
        }
    }
    
    if (!empty($permissionErrors)) {
        return ['success' => false, 'errors' => $permissionErrors];
    }
    
    $installSession['step2_requirements'] = true;
    return ['success' => true, 'next_step' => 3];
}

/**
 * Step 3: Database Configuration
 */
function processDatabaseStep() {
    global $installSession;
    
    $host = trim($_POST['db_host'] ?? '');
    $name = trim($_POST['db_name'] ?? '');
    $username = trim($_POST['db_username'] ?? '');
    $password = $_POST['db_password'] ?? '';
    
    $errors = [];
    
    if (empty($host)) $errors[] = 'Database host is required';
    if (empty($name)) $errors[] = 'Database name is required';
    if (empty($username)) $errors[] = 'Database username is required';
    
    if (!empty($errors)) {
        return ['success' => false, 'errors' => $errors];
    }
    
    // Test database connection
    try {
        $dsn = "mysql:host={$host};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        // Check if database exists, create if not
        $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
        $stmt->execute([$name]);
        
        if (!$stmt->fetch()) {
            $pdo->exec("CREATE DATABASE `{$name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        }
        
        // Test connection to the specific database
        $dsn = "mysql:host={$host};dbname={$name};charset=utf8mb4";
        $testPdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // Store database configuration
        $installSession['data']['database'] = [
            'host' => $host,
            'name' => $name,
            'username' => $username,
            'password' => $password
        ];
        
        $installSession['step3_database'] = true;
        return ['success' => true, 'next_step' => 4];
        
    } catch (PDOException $e) {
        return ['success' => false, 'errors' => ['Database connection failed: ' . $e->getMessage()]];
    }
}

/**
 * Step 4: Site Settings
 */
function processSettingsStep() {
    global $installSession;
    
    $gymName = trim($_POST['gym_name'] ?? '');
    $siteUrl = trim($_POST['site_url'] ?? '');
    $currency = trim($_POST['currency'] ?? 'USD');
    $timezone = trim($_POST['timezone'] ?? 'UTC');
    
    $errors = [];
    
    if (empty($gymName)) $errors[] = 'Gym name is required';
    if (empty($siteUrl)) $errors[] = 'Site URL is required';
    if (!filter_var($siteUrl, FILTER_VALIDATE_URL)) $errors[] = 'Valid site URL is required';
    
    if (!empty($errors)) {
        return ['success' => false, 'errors' => $errors];
    }
    
    // Store site settings
    $installSession['data']['settings'] = [
        'gym_name' => $gymName,
        'site_url' => $siteUrl,
        'currency' => $currency,
        'timezone' => $timezone
    ];
    
    $installSession['step4_settings'] = true;
    return ['success' => true, 'next_step' => 5];
}

/**
 * Step 5: Admin Account Creation
 */
function processAdminStep() {
    global $installSession;
    
    $name = trim($_POST['admin_name'] ?? '');
    $email = trim($_POST['admin_email'] ?? '');
    $password = $_POST['admin_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    $errors = [];
    
    if (empty($name)) $errors[] = 'Admin name is required';
    if (empty($email)) $errors[] = 'Admin email is required';
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'Valid email address is required';
    if (empty($password)) $errors[] = 'Password is required';
    if (strlen($password) < 6) $errors[] = 'Password must be at least 6 characters';
    if ($password !== $confirmPassword) $errors[] = 'Passwords do not match';
    
    if (!empty($errors)) {
        return ['success' => false, 'errors' => $errors];
    }
    
    // Store admin data
    $installSession['data']['admin'] = [
        'name' => $name,
        'email' => $email,
        'password' => $password // Store plain password for now, will hash during installation
    ];
    
    // Perform final installation
    return performInstallation();
}

/**
 * Perform the actual installation
 */
function performInstallation() {
    global $installSession;

    try {
        $dbConfig = $installSession['data']['database'];
        $settings = $installSession['data']['settings'];
        $admin = $installSession['data']['admin'];

        // Create database connection
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);

        // Execute database schema
        $schema = file_get_contents('schema.sql');
        if (!$schema) {
            throw new Exception('Could not read database schema file');
        }

        // Execute schema in transaction
        $pdo->beginTransaction();

        try {
            $pdo->exec($schema);

            // Hash password properly with verification
            $hashedPassword = password_hash($admin['password'], PASSWORD_DEFAULT);

            // Double-check password hash was created correctly
            if (!password_verify($admin['password'], $hashedPassword)) {
                throw new Exception('Password hashing verification failed - this is a critical error');
            }

            // Insert admin user
            $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, is_active, created_at) VALUES (?, ?, ?, 'admin', 1, NOW())");
            $stmt->execute([$admin['name'], $admin['email'], $hashedPassword]);

            $adminId = $pdo->lastInsertId();
            if (!$adminId) {
                throw new Exception('Failed to create admin user');
            }

            // Insert system settings
            $systemSettings = [
                'gym_name' => $settings['gym_name'],
                'site_url' => $settings['site_url'],
                'currency' => $settings['currency'],
                'timezone' => $settings['timezone'],
                'installation_date' => date('Y-m-d H:i:s'),
                'version' => '2.0'
            ];

            foreach ($systemSettings as $key => $value) {
                $stmt = $pdo->prepare("INSERT INTO settings (`key`, `value`, created_at) VALUES (?, ?, NOW())");
                $stmt->execute([$key, $value]);
            }

            $pdo->commit();

            // Create configuration file
            $configContent = "<?php\nreturn [\n";
            $configContent .= "    'database' => [\n";
            $configContent .= "        'host' => '{$dbConfig['host']}',\n";
            $configContent .= "        'name' => '{$dbConfig['name']}',\n";
            $configContent .= "        'username' => '{$dbConfig['username']}',\n";
            $configContent .= "        'password' => '{$dbConfig['password']}'\n";
            $configContent .= "    ],\n";
            $configContent .= "    'app' => [\n";
            $configContent .= "        'name' => '{$settings['gym_name']}',\n";
            $configContent .= "        'url' => '{$settings['site_url']}',\n";
            $configContent .= "        'installed' => true,\n";
            $configContent .= "        'version' => '2.0'\n";
            $configContent .= "    ]\n";
            $configContent .= "];\n";

            if (!file_put_contents('config/config.php', $configContent)) {
                throw new Exception('Could not create configuration file');
            }

            // Clear installation session
            unset($_SESSION['install_session']);

            return [
                'success' => true,
                'message' => 'Installation completed successfully!',
                'admin_email' => $admin['email'],
                'admin_name' => $admin['name']
            ];

        } catch (Exception $e) {
            $pdo->rollback();
            throw $e;
        }

    } catch (Exception $e) {
        return ['success' => false, 'errors' => ['Installation failed: ' . $e->getMessage()]];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MyGym Installation Wizard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .step-indicator {
            transition: all 0.3s ease;
        }
        .step-active {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }
        .step-completed {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }
        .step-inactive {
            background: #f3f4f6;
            color: #6b7280;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-2">MyGym Installation Wizard</h1>
            <p class="text-gray-600">Professional Gym Management System - Version 2.0</p>
        </div>

        <!-- Step Indicator -->
        <div class="flex justify-center mb-8">
            <div class="flex space-x-4">
                <?php for ($i = 1; $i <= 5; $i++): ?>
                    <div class="flex items-center">
                        <div class="step-indicator w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold
                            <?php
                                if ($i < $currentStep || ($i == $currentStep && $success)) {
                                    echo 'step-completed';
                                } elseif ($i == $currentStep) {
                                    echo 'step-active';
                                } else {
                                    echo 'step-inactive';
                                }
                            ?>">
                            <?php if ($i < $currentStep || ($i == $currentStep && $success)): ?>
                                <i class="fas fa-check"></i>
                            <?php else: ?>
                                <?= $i ?>
                            <?php endif; ?>
                        </div>
                        <?php if ($i < 5): ?>
                            <div class="w-8 h-0.5 bg-gray-300 mx-2"></div>
                        <?php endif; ?>
                    </div>
                <?php endfor; ?>
            </div>
        </div>

        <!-- Main Content -->
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8">

            <!-- Error Messages -->
            <?php if (!empty($errors)): ?>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <i class="fas fa-exclamation-triangle text-red-400 mt-1 mr-3"></i>
                        <div>
                            <h4 class="text-red-800 font-medium mb-2">Installation Error</h4>
                            <?php foreach ($errors as $error): ?>
                                <p class="text-red-700"><?= htmlspecialchars($error) ?></p>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Success Messages -->
            <?php if ($success && !isset($result['message'])): ?>
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <i class="fas fa-check-circle text-green-400 mt-1 mr-3"></i>
                        <div>
                            <p class="text-green-800">Step completed successfully! Proceeding to next step...</p>
                        </div>
                    </div>
                </div>
                <script>
                    setTimeout(function() {
                        window.location.href = 'install.php?step=<?= $currentStep ?>';
                    }, 1500);
                </script>
            <?php endif; ?>

            <!-- Step Content -->
            <?php
            switch ($currentStep) {
                case 1:
                    if (file_exists('install_steps/step1_eula.php')) {
                        include 'install_steps/step1_eula.php';
                    } else {
                        echo '<div class="text-center"><p class="text-red-600">Step 1 file not found. Please check installation files.</p></div>';
                    }
                    break;
                case 2:
                    if (file_exists('install_steps/step2_requirements.php')) {
                        include 'install_steps/step2_requirements.php';
                    } else {
                        echo '<div class="text-center"><p class="text-red-600">Step 2 file not found. Please check installation files.</p></div>';
                    }
                    break;
                case 3:
                    if (file_exists('install_steps/step3_database.php')) {
                        include 'install_steps/step3_database.php';
                    } else {
                        echo '<div class="text-center"><p class="text-red-600">Step 3 file not found. Please check installation files.</p></div>';
                    }
                    break;
                case 4:
                    if (file_exists('install_steps/step4_settings.php')) {
                        include 'install_steps/step4_settings.php';
                    } else {
                        echo '<div class="text-center"><p class="text-red-600">Step 4 file not found. Please check installation files.</p></div>';
                    }
                    break;
                case 5:
                    if (isset($result['message'])) {
                        if (file_exists('install_steps/step5_complete.php')) {
                            include 'install_steps/step5_complete.php';
                        } else {
                            echo '<div class="text-center"><p class="text-green-600">Installation completed successfully!</p></div>';
                        }
                    } else {
                        if (file_exists('install_steps/step5_admin.php')) {
                            include 'install_steps/step5_admin.php';
                        } else {
                            echo '<div class="text-center"><p class="text-red-600">Step 5 file not found. Please check installation files.</p></div>';
                        }
                    }
                    break;
            }
            ?>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8 text-gray-500 text-sm">
            <p>MyGym Management System &copy; 2025 - Professional Gym Management Solution</p>
        </div>
    </div>
</body>
</html>
