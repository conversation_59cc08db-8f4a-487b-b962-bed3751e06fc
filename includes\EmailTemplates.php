<?php
/**
 * Email Templates Class
 * MyGym Management System
 * 
 * Provides professional email templates for notifications
 */

require_once __DIR__ . '/Config.php';

class EmailTemplates {
    
    /**
     * Get available email templates
     */
    public static function getAvailableTemplates() {
        return [
            'welcome' => [
                'name' => 'Welcome New Member',
                'description' => 'Welcome message for new gym members',
                'icon' => 'fas fa-user-plus',
                'color' => 'blue'
            ],
            'membership_expiry' => [
                'name' => 'Membership Expiry Reminder',
                'description' => 'Remind members about upcoming membership expiry',
                'icon' => 'fas fa-clock',
                'color' => 'orange'
            ],
            'membership_expired' => [
                'name' => 'Membership Expired',
                'description' => 'Notify members that their membership has expired',
                'icon' => 'fas fa-exclamation-triangle',
                'color' => 'red'
            ],
            'payment_reminder' => [
                'name' => 'Payment Reminder',
                'description' => 'Remind members about pending payments',
                'icon' => 'fas fa-credit-card',
                'color' => 'red'
            ],
            'payment_confirmation' => [
                'name' => 'Payment Confirmation',
                'description' => 'Confirm successful payment receipt',
                'icon' => 'fas fa-check-circle',
                'color' => 'green'
            ],
            'class_announcement' => [
                'name' => 'Class Announcement',
                'description' => 'Announce new classes or schedule changes',
                'icon' => 'fas fa-dumbbell',
                'color' => 'green'
            ],
            'promotion' => [
                'name' => 'Special Promotion',
                'description' => 'Promote special offers and discounts',
                'icon' => 'fas fa-tags',
                'color' => 'purple'
            ],
            'maintenance' => [
                'name' => 'Maintenance Notice',
                'description' => 'Notify about gym maintenance or closures',
                'icon' => 'fas fa-tools',
                'color' => 'yellow'
            ],
            'birthday' => [
                'name' => 'Birthday Wishes',
                'description' => 'Send birthday greetings to members',
                'icon' => 'fas fa-birthday-cake',
                'color' => 'pink'
            ],
            'achievement' => [
                'name' => 'Achievement Congratulations',
                'description' => 'Congratulate members on their achievements',
                'icon' => 'fas fa-trophy',
                'color' => 'gold'
            ],
            'newsletter' => [
                'name' => 'Newsletter',
                'description' => 'Monthly newsletter with gym updates',
                'icon' => 'fas fa-newspaper',
                'color' => 'indigo'
            ],
            'custom' => [
                'name' => 'Custom Message',
                'description' => 'Create your own custom message',
                'icon' => 'fas fa-edit',
                'color' => 'gray'
            ]
        ];
    }

    /**
     * Generate email content based on template
     */
    public static function generateTemplate($templateType, $data = []) {
        $gymName = Config::get('gym_name') ?: 'MyGym';
        $gymEmail = Config::get('gym_email') ?: '';
        $gymPhone = Config::get('gym_phone') ?: '';
        $gymAddress = Config::get('gym_address') ?: '';
        
        // Default data
        $defaultData = [
            'member_name' => $data['member_name'] ?? 'Valued Member',
            'gym_name' => $gymName,
            'gym_email' => $gymEmail,
            'gym_phone' => $gymPhone,
            'gym_address' => $gymAddress,
            'date' => date('F j, Y'),
            'time' => date('g:i A')
        ];
        
        $data = array_merge($defaultData, $data);
        
        switch ($templateType) {
            case 'welcome':
                return self::getWelcomeTemplate($data);
            case 'membership_expiry':
                return self::getMembershipExpiryTemplate($data);
            case 'membership_expired':
                return self::getMembershipExpiredTemplate($data);
            case 'payment_reminder':
                return self::getPaymentReminderTemplate($data);
            case 'payment_confirmation':
                return self::getPaymentConfirmationTemplate($data);
            case 'class_announcement':
                return self::getClassAnnouncementTemplate($data);
            case 'promotion':
                return self::getPromotionTemplate($data);
            case 'maintenance':
                return self::getMaintenanceTemplate($data);
            case 'birthday':
                return self::getBirthdayTemplate($data);
            case 'achievement':
                return self::getAchievementTemplate($data);
            case 'newsletter':
                return self::getNewsletterTemplate($data);
            default:
                return self::getCustomTemplate($data);
        }
    }

    /**
     * Welcome New Member Template
     */
    private static function getWelcomeTemplate($data) {
        return [
            'subject' => "Welcome to {$data['gym_name']}! 🎉",
            'content' => "
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1 style='color: #3b82f6; font-size: 28px; margin-bottom: 10px;'>Welcome to {$data['gym_name']}!</h1>
                    <p style='font-size: 18px; color: #666;'>We're excited to have you join our fitness family!</p>
                </div>
                
                <div style='background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;'>
                    <h3 style='color: #1f2937; margin-top: 0;'>Dear {$data['member_name']},</h3>
                    <p>Welcome to {$data['gym_name']}! We're thrilled that you've decided to start your fitness journey with us.</p>
                    
                    <h4 style='color: #3b82f6;'>What's Next?</h4>
                    <ul style='color: #4b5563;'>
                        <li>📋 Complete your member profile</li>
                        <li>🏃‍♂️ Book your first workout session</li>
                        <li>💪 Meet with our trainers for a fitness assessment</li>
                        <li>📱 Download our mobile app for easy access</li>
                    </ul>
                    
                    <p>Our team is here to support you every step of the way. Don't hesitate to ask if you have any questions!</p>
                </div>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <p style='font-size: 16px; color: #059669;'><strong>Let's achieve your fitness goals together!</strong></p>
                </div>
            "
        ];
    }

    /**
     * Membership Expiry Reminder Template
     */
    private static function getMembershipExpiryTemplate($data) {
        $expiryDate = $data['expiry_date'] ?? 'soon';
        $daysLeft = $data['days_left'] ?? 'few';
        
        return [
            'subject' => "Membership Renewal Reminder - {$data['gym_name']}",
            'content' => "
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1 style='color: #f59e0b; font-size: 28px; margin-bottom: 10px;'>Membership Renewal Reminder</h1>
                    <p style='font-size: 18px; color: #666;'>Don't let your fitness journey pause!</p>
                </div>
                
                <div style='background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;'>
                    <h3 style='color: #92400e; margin-top: 0;'>Dear {$data['member_name']},</h3>
                    <p>Your membership at {$data['gym_name']} will expire on <strong>{$expiryDate}</strong> ({$daysLeft} days remaining).</p>
                    
                    <h4 style='color: #f59e0b;'>Renew Today and Continue Your Progress!</h4>
                    <ul style='color: #451a03;'>
                        <li>🎯 Keep your fitness momentum going</li>
                        <li>💪 Maintain access to all equipment and classes</li>
                        <li>👥 Stay connected with our fitness community</li>
                        <li>🏆 Continue tracking your achievements</li>
                    </ul>
                    
                    <p>Visit us or call <strong>{$data['gym_phone']}</strong> to renew your membership today!</p>
                </div>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <p style='font-size: 16px; color: #dc2626;'><strong>Don't miss out on your fitness goals!</strong></p>
                </div>
            "
        ];
    }

    /**
     * Membership Expired Template
     */
    private static function getMembershipExpiredTemplate($data) {
        $expiredDate = $data['expired_date'] ?? 'recently';

        return [
            'subject' => "Membership Expired - Renew Today at {$data['gym_name']}",
            'content' => "
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1 style='color: #dc2626; font-size: 28px; margin-bottom: 10px;'>Membership Expired</h1>
                    <p style='font-size: 18px; color: #666;'>We miss you at the gym!</p>
                </div>

                <div style='background: #fee2e2; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626;'>
                    <h3 style='color: #991b1b; margin-top: 0;'>Dear {$data['member_name']},</h3>
                    <p>Your membership at {$data['gym_name']} expired on <strong>{$expiredDate}</strong>.</p>

                    <p>We hope you've enjoyed your time with us and would love to have you back! Your fitness journey doesn't have to end here.</p>

                    <h4 style='color: #dc2626;'>Renew Today and Get Back to:</h4>
                    <ul style='color: #7f1d1d;'>
                        <li>💪 Full access to all gym equipment</li>
                        <li>🏃‍♂️ Group fitness classes</li>
                        <li>👨‍🏫 Personal training sessions</li>
                        <li>🏆 Progress tracking and achievements</li>
                        <li>👥 Our supportive fitness community</li>
                    </ul>

                    <p><strong>Special Offer:</strong> Renew within 7 days and get 10% off your next membership!</p>

                    <p>Visit us at {$data['gym_address']} or call <strong>{$data['gym_phone']}</strong> to renew your membership today.</p>
                </div>

                <div style='text-align: center; margin: 30px 0;'>
                    <p style='font-size: 16px; color: #059669;'><strong>We can't wait to see you back at the gym!</strong></p>
                </div>
            "
        ];
    }

    /**
     * Payment Reminder Template
     */
    private static function getPaymentReminderTemplate($data) {
        $amount = $data['amount'] ?? '0.00';
        $dueDate = $data['due_date'] ?? 'soon';
        
        return [
            'subject' => "Payment Reminder - {$data['gym_name']}",
            'content' => "
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1 style='color: #dc2626; font-size: 28px; margin-bottom: 10px;'>Payment Reminder</h1>
                    <p style='font-size: 18px; color: #666;'>Your payment is due soon</p>
                </div>
                
                <div style='background: #fee2e2; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626;'>
                    <h3 style='color: #991b1b; margin-top: 0;'>Dear {$data['member_name']},</h3>
                    <p>This is a friendly reminder that your payment of <strong>\${$amount}</strong> is due on <strong>{$dueDate}</strong>.</p>
                    
                    <h4 style='color: #dc2626;'>Payment Details:</h4>
                    <ul style='color: #7f1d1d;'>
                        <li>💰 Amount Due: \${$amount}</li>
                        <li>📅 Due Date: {$dueDate}</li>
                        <li>🏦 Payment Methods: Cash, Card, Online</li>
                        <li>📍 Pay at: {$data['gym_address']}</li>
                    </ul>
                    
                    <p>To avoid any interruption to your membership, please make your payment by the due date.</p>
                </div>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <p style='font-size: 16px; color: #059669;'><strong>Thank you for being a valued member!</strong></p>
                </div>
            "
        ];
    }

    /**
     * Payment Confirmation Template
     */
    private static function getPaymentConfirmationTemplate($data) {
        $amount = $data['amount'] ?? '0.00';
        $receiptNumber = $data['receipt_number'] ?? 'N/A';
        $paymentDate = $data['payment_date'] ?? date('F j, Y');
        $planName = $data['plan_name'] ?? 'Membership Plan';
        $paymentMethod = $data['payment_method'] ?? 'Cash';
        $startDate = $data['start_date'] ?? 'N/A';
        $endDate = $data['end_date'] ?? 'N/A';

        return [
            'subject' => "Payment Confirmation - {$data['gym_name']}",
            'content' => "
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1 style='color: #059669; font-size: 28px; margin-bottom: 10px;'>✅ Payment Confirmed!</h1>
                    <p style='font-size: 18px; color: #666;'>Thank you for your payment</p>
                </div>

                <div style='background: #d1fae5; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #059669;'>
                    <h3 style='color: #065f46; margin-top: 0;'>Dear {$data['member_name']},</h3>
                    <p>We have successfully received your payment of <strong>\${$amount}</strong> for your {$planName}.</p>

                    <h4 style='color: #059669;'>Payment Details:</h4>
                    <ul style='color: #064e3b;'>
                        <li>💰 Amount Paid: \${$amount}</li>
                        <li>🧾 Receipt Number: {$receiptNumber}</li>
                        <li>📅 Payment Date: {$paymentDate}</li>
                        <li>💳 Payment Method: {$paymentMethod}</li>
                        <li>📋 Plan: {$planName}</li>
                        <li>🗓️ Membership Period: {$startDate} to {$endDate}</li>
                    </ul>

                    <p><strong>Your membership is now active and ready to use!</strong></p>
                </div>

                <div style='background: #f0f9ff; padding: 15px; border-radius: 8px; margin: 20px 0;'>
                    <h4 style='color: #0369a1; margin-top: 0;'>What's Next?</h4>
                    <ul style='color: #0c4a6e;'>
                        <li>🏋️ Visit the gym and start your workout</li>
                        <li>📱 Download our mobile app (if available)</li>
                        <li>👥 Join our fitness community</li>
                        <li>🎯 Set your fitness goals</li>
                    </ul>
                </div>

                <div style='text-align: center; margin: 30px 0;'>
                    <p style='font-size: 16px; color: #059669;'><strong>Welcome to your fitness journey!</strong></p>
                </div>
            "
        ];
    }

    /**
     * Class Announcement Template
     */
    private static function getClassAnnouncementTemplate($data) {
        $className = $data['class_name'] ?? 'New Fitness Class';
        $classTime = $data['class_time'] ?? 'TBD';
        $instructor = $data['instructor'] ?? 'Our Expert Trainer';
        
        return [
            'subject' => "New Class Alert: {$className} - {$data['gym_name']}",
            'content' => "
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1 style='color: #059669; font-size: 28px; margin-bottom: 10px;'>New Class Announcement!</h1>
                    <p style='font-size: 18px; color: #666;'>Join our exciting new fitness class</p>
                </div>
                
                <div style='background: #ecfdf5; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #059669;'>
                    <h3 style='color: #065f46; margin-top: 0;'>Dear {$data['member_name']},</h3>
                    <p>We're excited to announce our new class: <strong>{$className}</strong>!</p>
                    
                    <h4 style='color: #059669;'>Class Details:</h4>
                    <ul style='color: #064e3b;'>
                        <li>🏃‍♂️ Class: {$className}</li>
                        <li>⏰ Time: {$classTime}</li>
                        <li>👨‍🏫 Instructor: {$instructor}</li>
                        <li>📍 Location: {$data['gym_name']}</li>
                    </ul>
                    
                    <p>This class is perfect for all fitness levels. Come join us and take your workout to the next level!</p>
                </div>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <p style='font-size: 16px; color: #7c3aed;'><strong>Book your spot today!</strong></p>
                </div>
            "
        ];
    }

    /**
     * Promotion Template
     */
    private static function getPromotionTemplate($data) {
        $offerTitle = $data['offer_title'] ?? 'Special Offer';
        $discount = $data['discount'] ?? '20%';
        $validUntil = $data['valid_until'] ?? 'limited time';

        return [
            'subject' => "🎉 Special Offer: {$offerTitle} - {$data['gym_name']}",
            'content' => "
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1 style='color: #7c3aed; font-size: 28px; margin-bottom: 10px;'>🎉 Special Promotion!</h1>
                    <p style='font-size: 18px; color: #666;'>{$offerTitle}</p>
                </div>

                <div style='background: #f3e8ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #7c3aed;'>
                    <h3 style='color: #581c87; margin-top: 0;'>Dear {$data['member_name']},</h3>
                    <p>We have an amazing offer just for you! Get <strong>{$discount} OFF</strong> on your next membership renewal.</p>

                    <h4 style='color: #7c3aed;'>Offer Details:</h4>
                    <ul style='color: #4c1d95;'>
                        <li>💰 Discount: {$discount}</li>
                        <li>⏰ Valid Until: {$validUntil}</li>
                        <li>🎯 Applicable to: Membership renewals</li>
                        <li>📞 Contact: {$data['gym_phone']}</li>
                    </ul>

                    <p>Don't miss out on this exclusive offer! Visit us today to take advantage of this limited-time promotion.</p>
                </div>

                <div style='text-align: center; margin: 30px 0;'>
                    <p style='font-size: 16px; color: #dc2626;'><strong>Hurry! Limited time offer!</strong></p>
                </div>
            "
        ];
    }

    /**
     * Maintenance Notice Template
     */
    private static function getMaintenanceTemplate($data) {
        $maintenanceDate = $data['maintenance_date'] ?? 'upcoming date';
        $duration = $data['duration'] ?? 'few hours';
        $reason = $data['reason'] ?? 'equipment maintenance';

        return [
            'subject' => "Maintenance Notice - {$data['gym_name']}",
            'content' => "
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1 style='color: #f59e0b; font-size: 28px; margin-bottom: 10px;'>🔧 Maintenance Notice</h1>
                    <p style='font-size: 18px; color: #666;'>Temporary closure for improvements</p>
                </div>

                <div style='background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;'>
                    <h3 style='color: #92400e; margin-top: 0;'>Dear {$data['member_name']},</h3>
                    <p>We will be temporarily closed on <strong>{$maintenanceDate}</strong> for {$reason}.</p>

                    <h4 style='color: #f59e0b;'>Maintenance Details:</h4>
                    <ul style='color: #451a03;'>
                        <li>📅 Date: {$maintenanceDate}</li>
                        <li>⏱️ Duration: {$duration}</li>
                        <li>🔧 Reason: {$reason}</li>
                        <li>🎯 Goal: Better facilities for you!</li>
                    </ul>

                    <p>We apologize for any inconvenience and appreciate your understanding as we work to improve your gym experience.</p>
                </div>

                <div style='text-align: center; margin: 30px 0;'>
                    <p style='font-size: 16px; color: #059669;'><strong>Thank you for your patience!</strong></p>
                </div>
            "
        ];
    }

    /**
     * Birthday Template
     */
    private static function getBirthdayTemplate($data) {
        return [
            'subject' => "🎂 Happy Birthday from {$data['gym_name']}!",
            'content' => "
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1 style='color: #ec4899; font-size: 28px; margin-bottom: 10px;'>🎂 Happy Birthday!</h1>
                    <p style='font-size: 18px; color: #666;'>Celebrating another year of awesome!</p>
                </div>

                <div style='background: #fdf2f8; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ec4899;'>
                    <h3 style='color: #be185d; margin-top: 0;'>Dear {$data['member_name']},</h3>
                    <p>🎉 Happy Birthday! The entire {$data['gym_name']} family wishes you a fantastic day filled with joy, laughter, and celebration!</p>

                    <h4 style='color: #ec4899;'>Birthday Special:</h4>
                    <ul style='color: #831843;'>
                        <li>🎁 Free personal training session</li>
                        <li>🥤 Complimentary protein shake</li>
                        <li>📸 Birthday photo for our wall of fame</li>
                        <li>🎈 Special birthday workout playlist</li>
                    </ul>

                    <p>Come celebrate with us and make this birthday extra special with a great workout!</p>
                </div>

                <div style='text-align: center; margin: 30px 0;'>
                    <p style='font-size: 16px; color: #7c3aed;'><strong>Here's to another year of fitness and fun! 🎊</strong></p>
                </div>
            "
        ];
    }

    /**
     * Achievement Template
     */
    private static function getAchievementTemplate($data) {
        $achievement = $data['achievement'] ?? 'reaching your fitness goal';

        return [
            'subject' => "🏆 Congratulations on Your Achievement! - {$data['gym_name']}",
            'content' => "
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1 style='color: #f59e0b; font-size: 28px; margin-bottom: 10px;'>🏆 Congratulations!</h1>
                    <p style='font-size: 18px; color: #666;'>You've achieved something amazing!</p>
                </div>

                <div style='background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;'>
                    <h3 style='color: #92400e; margin-top: 0;'>Dear {$data['member_name']},</h3>
                    <p>🎉 Congratulations on <strong>{$achievement}</strong>! Your dedication and hard work have truly paid off.</p>

                    <h4 style='color: #f59e0b;'>Your Success Story:</h4>
                    <ul style='color: #451a03;'>
                        <li>💪 Achievement: {$achievement}</li>
                        <li>📅 Date: {$data['date']}</li>
                        <li>🎯 Next Goal: Keep pushing forward!</li>
                        <li>👥 Inspiration: You motivate others!</li>
                    </ul>

                    <p>You're an inspiration to our entire gym community. Keep up the fantastic work!</p>
                </div>

                <div style='text-align: center; margin: 30px 0;'>
                    <p style='font-size: 16px; color: #dc2626;'><strong>You're a champion! 🌟</strong></p>
                </div>
            "
        ];
    }

    /**
     * Newsletter Template
     */
    private static function getNewsletterTemplate($data) {
        $month = $data['month'] ?? date('F Y');

        return [
            'subject' => "📰 {$data['gym_name']} Newsletter - {$month}",
            'content' => "
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1 style='color: #6366f1; font-size: 28px; margin-bottom: 10px;'>📰 Monthly Newsletter</h1>
                    <p style='font-size: 18px; color: #666;'>{$month} Edition</p>
                </div>

                <div style='background: #eef2ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #6366f1;'>
                    <h3 style='color: #3730a3; margin-top: 0;'>Dear {$data['member_name']},</h3>
                    <p>Welcome to our monthly newsletter! Here's what's happening at {$data['gym_name']} this month.</p>

                    <h4 style='color: #6366f1;'>This Month's Highlights:</h4>
                    <ul style='color: #312e81;'>
                        <li>🆕 New equipment arrivals</li>
                        <li>📅 Upcoming fitness challenges</li>
                        <li>👨‍🏫 Featured trainer spotlight</li>
                        <li>🏆 Member achievements</li>
                        <li>💡 Fitness tips and nutrition advice</li>
                    </ul>

                    <p>Stay tuned for more exciting updates and keep crushing your fitness goals!</p>
                </div>

                <div style='text-align: center; margin: 30px 0;'>
                    <p style='font-size: 16px; color: #059669;'><strong>Together we achieve more! 💪</strong></p>
                </div>
            "
        ];
    }

    /**
     * Custom Template
     */
    private static function getCustomTemplate($data) {
        $customMessage = $data['custom_message'] ?? 'Your custom message here...';
        $customSubject = $data['custom_subject'] ?? 'Message from ' . $data['gym_name'];

        return [
            'subject' => $customSubject,
            'content' => "
                <div style='background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;'>
                    <h3 style='color: #1f2937; margin-top: 0;'>Dear {$data['member_name']},</h3>
                    <div style='color: #4b5563; line-height: 1.6;'>
                        {$customMessage}
                    </div>
                </div>

                <div style='text-align: center; margin: 30px 0;'>
                    <p style='font-size: 16px; color: #6b7280;'><strong>Thank you for being part of our community!</strong></p>
                </div>
            "
        ];
    }

    /**
     * Get base HTML template structure
     */
    public static function wrapInTemplate($content, $subject = '') {
        $gymName = Config::get('gym_name') ?: 'MyGym';
        $gymEmail = Config::get('gym_email') ?: '';
        $gymPhone = Config::get('gym_phone') ?: '';
        $gymAddress = Config::get('gym_address') ?: '';
        
        return "
        <!DOCTYPE html>
        <html lang='en'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>{$subject}</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f7fafc; }
                .container { max-width: 600px; margin: 0 auto; background: white; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
                .header h1 { margin: 0; font-size: 28px; font-weight: bold; }
                .header p { margin: 10px 0 0 0; opacity: 0.9; font-size: 16px; }
                .content { padding: 40px 30px; }
                .footer { background: #f8fafc; padding: 30px; text-align: center; border-top: 1px solid #e2e8f0; }
                .footer p { margin: 5px 0; color: #64748b; font-size: 14px; }
                .footer a { color: #3b82f6; text-decoration: none; }
                .social-links { margin: 20px 0; }
                .social-links a { display: inline-block; margin: 0 10px; color: #64748b; font-size: 20px; }
                h1, h2, h3, h4 { color: #1f2937; }
                .highlight { background: #fef3c7; padding: 2px 6px; border-radius: 4px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>💪 {$gymName}</h1>
                    <p>Your Fitness Journey Starts Here</p>
                </div>
                <div class='content'>
                    {$content}
                </div>
                <div class='footer'>
                    <p><strong>{$gymName}</strong></p>
                    " . ($gymAddress ? "<p>📍 {$gymAddress}</p>" : "") . "
                    " . ($gymPhone ? "<p>📞 {$gymPhone}</p>" : "") . "
                    " . ($gymEmail ? "<p>✉️ <a href='mailto:{$gymEmail}'>{$gymEmail}</a></p>" : "") . "
                    
                    <div class='social-links'>
                        <a href='#'>📘</a>
                        <a href='#'>📷</a>
                        <a href='#'>🐦</a>
                    </div>
                    
                    <p style='font-size: 12px; color: #9ca3af; margin-top: 20px;'>
                        You received this email because you are a member of {$gymName}.<br>
                        If you no longer wish to receive these emails, please contact us.
                    </p>
                </div>
            </div>
        </body>
        </html>";
    }
}
