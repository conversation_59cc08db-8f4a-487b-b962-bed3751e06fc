<?php
/**
 * Print Receipt - Optimized for Thermal/Receipt Printers
 * MyGym Management System
 */

session_start();

// Check authentication
if (!isset($_SESSION['user_id']) && !isset($_SESSION['local_admin_username'])) {
    http_response_code(401);
    exit('Unauthorized');
}

// Get payment ID
$paymentId = $_GET['id'] ?? null;
if (!$paymentId || !is_numeric($paymentId)) {
    http_response_code(400);
    exit('Invalid payment ID');
}

$autoPrint = isset($_GET['auto_print']) && $_GET['auto_print'] == '1';

try {
    // Include required files
    require_once '../config/database.php';
    require_once '../includes/auth.php';

    $db = Database::getInstance();

    // Get payment details (NO sensitive information for security)
    $payment = $db->fetch("
        SELECT p.*,
               CONCAT(m.first_name, ' ', m.last_name) as member_name,
               m.member_id,
               pl.name as plan_name, pl.duration_months,
               u.name as processed_by_name
        FROM payments p
        LEFT JOIN members m ON p.member_id = m.id
        LEFT JOIN plans pl ON p.plan_id = pl.id
        LEFT JOIN users u ON p.processed_by = u.id
        WHERE p.id = ?
    ", [$paymentId]);

    if (!$payment) {
        http_response_code(404);
        exit('Payment not found');
    }

    // Get gym settings with fallbacks
    try {
        $gymName = Config::get('gym_name', 'MyGym');
        $gymAddress = Config::get('gym_address', '');
        $gymPhone = Config::get('gym_phone', '');
        $gymEmail = Config::get('gym_email', '');
        $currency = Config::get('currency', 'USD');
    } catch (Exception $configError) {
        // Fallback to defaults if Config/settings table doesn't exist
        $gymName = 'MyGym';
        $gymAddress = '';
        $gymPhone = '';
        $gymEmail = '';
        $currency = 'USD';
    }

    // formatCurrency function is already available from auth.php

} catch (Exception $e) {
    http_response_code(500);
    exit('Error retrieving payment details: ' . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt - <?= htmlspecialchars($payment['receipt_number']) ?></title>
    <style>
        /* Print-optimized styles for thermal/receipt printers */
        @media print {
            @page {
                size: 80mm auto;
                margin: 0;
            }
            body { margin: 0; }
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 10px;
            width: 80mm;
            max-width: 80mm;
            background: white;
        }
        
        .receipt {
            width: 100%;
            text-align: center;
        }
        
        .header {
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        
        .gym-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .contact-info {
            font-size: 10px;
            margin-bottom: 3px;
        }
        
        .receipt-title {
            font-size: 14px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .receipt-number {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .section {
            text-align: left;
            margin: 10px 0;
            border-bottom: 1px dashed #000;
            padding-bottom: 8px;
        }
        
        .row {
            display: flex;
            justify-content: space-between;
            margin: 3px 0;
        }
        
        .label {
            font-weight: bold;
        }
        
        .total {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
            border: 1px solid #000;
            padding: 8px;
        }
        
        .footer {
            text-align: center;
            font-size: 10px;
            margin-top: 15px;
            border-top: 1px dashed #000;
            padding-top: 10px;
        }
        
        .no-print {
            display: block;
        }
        
        @media print {
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <!-- Header -->
        <div class="header">
            <div class="gym-name"><?= htmlspecialchars($gymName) ?></div>
            <?php if ($gymAddress): ?>
                <div class="contact-info"><?= htmlspecialchars($gymAddress) ?></div>
            <?php endif; ?>
            <?php if ($gymPhone): ?>
                <div class="contact-info">Tel: <?= htmlspecialchars($gymPhone) ?></div>
            <?php endif; ?>
            <?php if ($gymEmail): ?>
                <div class="contact-info"><?= htmlspecialchars($gymEmail) ?></div>
            <?php endif; ?>
        </div>
        
        <!-- Receipt Title -->
        <div class="receipt-title">PAYMENT RECEIPT</div>
        <div class="receipt-number"><?= htmlspecialchars($payment['receipt_number']) ?></div>
        
        <!-- Member Information (NO sensitive data for security) -->
        <div class="section">
            <div class="row">
                <span class="label">Member:</span>
                <span><?= htmlspecialchars($payment['member_name']) ?></span>
            </div>
            <div class="row">
                <span class="label">Member ID:</span>
                <span><?= htmlspecialchars($payment['member_id']) ?></span>
            </div>
        </div>
        
        <!-- Payment Details -->
        <div class="section">
            <div class="row">
                <span class="label">Plan:</span>
                <span><?= htmlspecialchars($payment['plan_name'] ?? 'N/A') ?></span>
            </div>
            <div class="row">
                <span class="label">Duration:</span>
                <span><?= $payment['duration_months'] ?? 0 ?> month(s)</span>
            </div>
            <div class="row">
                <span class="label">Method:</span>
                <span><?= ucfirst(htmlspecialchars($payment['payment_method'])) ?></span>
            </div>
            <div class="row">
                <span class="label">Date:</span>
                <span><?= date('M j, Y', strtotime($payment['payment_date'])) ?></span>
            </div>
        </div>
        
        <!-- Membership Period -->
        <div class="section">
            <div class="row">
                <span class="label">Start:</span>
                <span><?= date('M j, Y', strtotime($payment['start_date'])) ?></span>
            </div>
            <div class="row">
                <span class="label">End:</span>
                <span><?= date('M j, Y', strtotime($payment['end_date'])) ?></span>
            </div>
        </div>
        
        <!-- Total Amount -->
        <div class="total">
            TOTAL: <?= formatCurrency($payment['amount'], $currency) ?>
        </div>
        
        <?php if ($payment['transaction_id']): ?>
        <div class="section">
            <div class="row">
                <span class="label">Transaction ID:</span>
                <span><?= htmlspecialchars($payment['transaction_id']) ?></span>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if ($payment['processed_by_name']): ?>
        <div class="section">
            <div class="row">
                <span class="label">Processed by:</span>
                <span><?= htmlspecialchars($payment['processed_by_name']) ?></span>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Footer -->
        <div class="footer">
            <div>Thank you for your membership!</div>
            <div style="margin-top: 5px;">
                <?= date('M j, Y g:i A') ?>
            </div>
        </div>
        
        <!-- Print Button (hidden when printing) -->
        <div class="no-print" style="text-align: center; margin-top: 20px;">
            <button onclick="window.print()" 
                    style="background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; font-size: 14px;">
                🖨️ Print Receipt
            </button>
            <button onclick="window.close()" 
                    style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; font-size: 14px; margin-left: 10px;">
                ❌ Close
            </button>
        </div>
    </div>
    
    <?php if ($autoPrint): ?>
    <script>
        // Auto-print when page loads (for direct printing)
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
    <?php endif; ?>
</body>
</html>
