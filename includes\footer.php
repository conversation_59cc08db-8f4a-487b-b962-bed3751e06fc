            </main>
        </div>
    </div>
    
    <!-- Mobile sidebar overlay -->
    <div x-show="sidebarOpen" 
         @click="sidebarOpen = false"
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"></div>
    
    <!-- Toast Container -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Enhanced Styles -->
    <style>
        @keyframes shrink {
            from { width: 100%; }
            to { width: 0%; }
        }

        .toast {
            backdrop-filter: blur(10px);
        }

        /* Enhanced modal animations */
        .modal-enter {
            animation: modalEnter 0.3s ease-out;
        }

        @keyframes modalEnter {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-10px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* Search input enhancements */
        .search-input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Button hover effects */
        .btn-hover {
            transition: all 0.2s ease;
        }

        .btn-hover:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Loading spinner */
        .spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Enhanced flash messages */
        .flash-message {
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>

    <!-- Global JavaScript -->
    <script>
        // Enhanced Toast notification system
        function showToast(message, type = 'info', duration = 5000) {
            // Create container if it doesn't exist
            let container = document.getElementById('toast-container');
            if (!container) {
                container = document.createElement('div');
                container.id = 'toast-container';
                container.className = 'fixed top-4 right-4 z-50 space-y-2';
                document.body.appendChild(container);
            }

            const toast = document.createElement('div');

            const icons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };

            const colors = {
                success: 'bg-gradient-to-r from-green-50 to-green-100 border-green-200 text-green-800 dark:from-green-900 dark:to-green-800 dark:border-green-700 dark:text-green-200',
                error: 'bg-gradient-to-r from-red-50 to-red-100 border-red-200 text-red-800 dark:from-red-900 dark:to-red-800 dark:border-red-700 dark:text-red-200',
                warning: 'bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200 text-yellow-800 dark:from-yellow-900 dark:to-yellow-800 dark:border-yellow-700 dark:text-yellow-200',
                info: 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 text-blue-800 dark:from-blue-900 dark:to-blue-800 dark:border-blue-700 dark:text-blue-200'
            };

            const iconColors = {
                success: 'text-green-500',
                error: 'text-red-500',
                warning: 'text-yellow-500',
                info: 'text-blue-500'
            };

            toast.className = `${colors[type]} border rounded-2xl p-4 shadow-xl toast max-w-sm transform transition-all duration-300 ease-out translate-x-full opacity-0`;
            toast.innerHTML = `
                <div class="flex items-start">
                    <div class="w-8 h-8 rounded-full bg-white dark:bg-gray-700 flex items-center justify-center mr-3 shadow-sm">
                        <i class="${icons[type]} ${iconColors[type]} text-sm"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium leading-5">${message}</p>
                    </div>
                    <button onclick="removeToast(this.closest('.toast'))" class="ml-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 flex-shrink-0">
                        <i class="fas fa-times text-xs"></i>
                    </button>
                </div>
                <div class="absolute bottom-0 left-0 h-1 bg-current opacity-20 rounded-full transition-all duration-${duration}" style="width: 100%; animation: shrink ${duration}ms linear;"></div>
            `;

            container.appendChild(toast);

            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full', 'opacity-0');
                toast.classList.add('translate-x-0', 'opacity-100');
            }, 10);

            // Auto remove after duration
            setTimeout(() => {
                removeToast(toast);
            }, duration);
        }

        function removeToast(toast) {
            if (toast && toast.parentElement) {
                toast.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 300);
            }
        }

        // Enhanced confirmation modal system
        function showConfirmModal(options = {}) {
            const {
                title = 'Confirm Action',
                message = 'Are you sure you want to proceed?',
                confirmText = 'Confirm',
                cancelText = 'Cancel',
                type = 'warning', // warning, danger, info
                onConfirm = () => {},
                onCancel = () => {}
            } = options;

            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';

            const typeColors = {
                warning: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-400',
                danger: 'bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400',
                info: 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
            };

            const typeIcons = {
                warning: 'fas fa-exclamation-triangle',
                danger: 'fas fa-exclamation-circle',
                info: 'fas fa-info-circle'
            };

            const confirmColors = {
                warning: 'bg-yellow-600 hover:bg-yellow-700',
                danger: 'bg-red-600 hover:bg-red-700',
                info: 'bg-blue-600 hover:bg-blue-700'
            };

            modal.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-md w-full mx-4 shadow-xl modal-enter">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 ${typeColors[type]} rounded-full flex items-center justify-center mr-4">
                            <i class="${typeIcons[type]} text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">${title}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">This action requires confirmation</p>
                        </div>
                    </div>
                    <div class="mb-6">
                        <p class="text-gray-700 dark:text-gray-300">${message}</p>
                    </div>
                    <div class="flex space-x-3">
                        <button class="cancel-btn flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition duration-200">
                            ${cancelText}
                        </button>
                        <button class="confirm-btn flex-1 px-4 py-2 ${confirmColors[type]} text-white rounded-lg transition duration-200">
                            ${confirmText}
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Event listeners
            const cancelBtn = modal.querySelector('.cancel-btn');
            const confirmBtn = modal.querySelector('.confirm-btn');

            cancelBtn.addEventListener('click', () => {
                modal.remove();
                onCancel();
            });

            confirmBtn.addEventListener('click', () => {
                modal.remove();
                onConfirm();
            });

            // Close on outside click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                    onCancel();
                }
            });

            // Close on escape key
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    modal.remove();
                    onCancel();
                    document.removeEventListener('keydown', handleEscape);
                }
            };
            document.addEventListener('keydown', handleEscape);

            return modal;
        }
        
        // Global search functionality for Alpine.js
        function globalSearch() {
            return {
                searchQuery: '',
                searchOpen: false,
                results: [],
                loading: false,
                searchTimeout: null,

                performSearch() {
                    if (this.searchQuery.length < 2) {
                        this.results = [];
                        return;
                    }

                    clearTimeout(this.searchTimeout);
                    this.loading = true;

                    this.searchTimeout = setTimeout(() => {
                        // Determine API path based on current location
                        const currentPath = window.location.pathname;
                        const isInPagesDir = currentPath.includes('/pages/');
                        const apiPath = isInPagesDir ? '../api/search.php' : 'api/search.php';

                        fetch(`${apiPath}?q=${encodeURIComponent(this.searchQuery)}`)
                            .then(response => response.json())
                            .then(data => {
                                this.loading = false;
                                if (data.success) {
                                    this.results = data.results || [];
                                } else {
                                    console.error('Search error:', data.message);
                                    this.results = [];
                                }
                            })
                            .catch(error => {
                                this.loading = false;
                                console.error('Search error:', error);
                                this.results = [];
                            });
                    }, 300);
                },

                getTypeIcon(type) {
                    const icons = {
                        'member': 'fas fa-user',
                        'payment': 'fas fa-receipt',
                        'trainer': 'fas fa-dumbbell',
                        'plan': 'fas fa-clipboard-list',
                        'equipment': 'fas fa-cogs'
                    };
                    return icons[type] || 'fas fa-circle';
                },

                getTypeBadgeClass(type) {
                    const classes = {
                        'member': 'bg-blue-100 text-blue-800',
                        'payment': 'bg-green-100 text-green-800',
                        'trainer': 'bg-purple-100 text-purple-800',
                        'plan': 'bg-orange-100 text-orange-800',
                        'equipment': 'bg-gray-100 text-gray-800'
                    };
                    return classes[type] || 'bg-gray-100 text-gray-800';
                }
            }
        }
        
        // Auto-hide flash messages
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.toast');
            flashMessages.forEach(message => {
                setTimeout(() => {
                    if (message.parentElement) {
                        message.style.opacity = '0';
                        message.style.transform = 'translateX(100%)';
                        setTimeout(() => message.remove(), 300);
                    }
                }, 5000);
            });
        });
        
        // Enhanced confirm delete actions
        function confirmDelete(message = 'Are you sure you want to delete this item?', callback = null) {
            return new Promise((resolve) => {
                showConfirmModal({
                    title: 'Delete Confirmation',
                    message: message,
                    confirmText: 'Delete',
                    cancelText: 'Cancel',
                    type: 'danger',
                    onConfirm: () => {
                        if (callback) callback();
                        resolve(true);
                    },
                    onCancel: () => {
                        resolve(false);
                    }
                });
            });
        }
        
        // Format currency
        function formatCurrency(amount, currency = 'USD') {
            const symbols = {
                'USD': '$',
                'EUR': '€',
                'GBP': '£',
                'CAD': 'C$',
                'AUD': 'A$',
                'INR': '₹',
                'JPY': '¥'
            };
            
            const symbol = symbols[currency] || currency;
            return symbol + parseFloat(amount).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }
        
        // Format date
        function formatDate(dateString, format = 'short') {
            const date = new Date(dateString);
            const options = {
                short: { year: 'numeric', month: 'short', day: 'numeric' },
                long: { year: 'numeric', month: 'long', day: 'numeric' },
                time: { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' }
            };
            
            return date.toLocaleDateString('en-US', options[format] || options.short);
        }
        
        // Loading state helper
        function setLoading(element, loading = true) {
            if (loading) {
                element.disabled = true;
                element.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
            } else {
                element.disabled = false;
                // Restore original text (you might want to store this)
            }
        }
        
        // AJAX helper
        function ajaxRequest(url, options = {}) {
            const defaults = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };
            
            return fetch(url, { ...defaults, ...options })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                });
        }
        
        // Form validation helper
        function validateForm(form) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('border-red-500');
                    isValid = false;
                } else {
                    field.classList.remove('border-red-500');
                }
            });
            
            return isValid;
        }
        
        // Auto-save functionality
        function autoSave(formId, endpoint) {
            const form = document.getElementById(formId);
            if (!form) return;
            
            let saveTimeout;
            const inputs = form.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                input.addEventListener('input', () => {
                    clearTimeout(saveTimeout);
                    saveTimeout = setTimeout(() => {
                        const formData = new FormData(form);
                        
                        fetch(endpoint, {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                showToast('Changes saved automatically', 'success', 2000);
                            }
                        })
                        .catch(error => {
                            console.error('Auto-save error:', error);
                        });
                    }, 2000);
                });
            });
        }
        
        // Dark mode persistence
        document.addEventListener('alpine:init', () => {
            Alpine.store('darkMode', {
                on: localStorage.getItem('darkMode') === 'true',
                
                toggle() {
                    this.on = !this.on;
                    localStorage.setItem('darkMode', this.on);
                    document.documentElement.classList.toggle('dark', this.on);
                }
            });
            
            // Apply saved dark mode preference
            if (localStorage.getItem('darkMode') === 'true') {
                document.documentElement.classList.add('dark');
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('input[placeholder*="Search"]');
                if (searchInput) {
                    searchInput.focus();
                    searchInput.select();
                }
            }

            // Escape to close modals/dropdowns and search
            if (e.key === 'Escape') {
                // Close search dropdown
                const searchInput = document.querySelector('input[placeholder*="Search"]');
                if (searchInput) {
                    searchInput.blur();
                }
                // Close any open Alpine.js dropdowns
                document.dispatchEvent(new CustomEvent('click'));
            }
        });

        // Simple page transitions - like original
        document.addEventListener('DOMContentLoaded', function() {
            // Remove any flash-preventing classes
            document.body.classList.remove('page-loading');

            // Simple page transition handling
            const links = document.querySelectorAll('a[href]:not([href^="#"]):not([href^="javascript:"]):not([target="_blank"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Only apply to internal navigation
                    if (this.hostname === window.location.hostname && !this.hasAttribute('download')) {
                        // Add simple transition effect only to main content - sidebar stays fixed
                        document.body.classList.add('page-transitioning');
                    }
                });
            });
        });

        // Reset dropdowns on page load to prevent flash
        window.addEventListener('pageshow', function() {
            // Reset all Alpine.js dropdown states
            if (window.Alpine) {
                document.querySelectorAll('[x-data]').forEach(el => {
                    if (el._x_dataStack && el._x_dataStack[0]) {
                        const data = el._x_dataStack[0];
                        if (data.searchOpen) data.searchOpen = false;
                        if (data.notificationOpen) data.notificationOpen = false;
                        if (data.userMenuOpen) data.userMenuOpen = false;
                    }
                });
            }
        });
        
        // View receipt in modal
        function viewReceipt(receiptId) {
            // Determine the correct API path based on current location
            const currentPath = window.location.pathname;
            const isInPagesDir = currentPath.includes('/pages/');
            const apiPath = isInPagesDir ? '../api/get-receipt.php' : 'api/get-receipt.php';

            // Fetch receipt data
            fetch(`${apiPath}?id=${receiptId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(text => {
                    console.log('Raw API response:', text);
                    try {
                        const data = JSON.parse(text);
                        if (data.success) {
                            showReceiptModal(data.receipt);
                        } else {
                            alert('Error loading receipt: ' + data.message);
                        }
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        console.error('Response text:', text);
                        alert('Error parsing receipt data');
                    }
                })
                .catch(error => {
                    console.error('Fetch error:', error);
                    alert('Error loading receipt: ' + error.message);
                });
        }

        // Print receipt
        function printReceipt(receiptId, thermal = false, format = 'html') {
            const currentPath = window.location.pathname;
            const isInPagesDir = currentPath.includes('/pages/');

            let apiPath;
            if (thermal) {
                if (format === 'text') {
                    apiPath = isInPagesDir ? '../api/thermal-receipt.php' : 'api/thermal-receipt.php';
                    // For thermal printers, open as text file
                    window.open(`${apiPath}?id=${receiptId}`, '_blank');
                } else {
                    apiPath = isInPagesDir ? '../api/thermal-receipt-html.php' : 'api/thermal-receipt-html.php';
                    const printWindow = window.open(`${apiPath}?id=${receiptId}&auto_print=1`, '_blank', 'width=400,height=700');
                    if (printWindow) {
                        printWindow.focus();
                    } else {
                        alert('Please allow popups to print receipts');
                    }
                }
            } else {
                apiPath = isInPagesDir ? '../api/print-receipt.php' : 'api/print-receipt.php';
                const printWindow = window.open(`${apiPath}?id=${receiptId}&auto_print=1`, '_blank', 'width=400,height=600');
                if (printWindow) {
                    printWindow.focus();
                } else {
                    alert('Please allow popups to print receipts');
                }
            }
        }

        // Modal functions
        function showModal(options) {
            const modal = document.getElementById('confirmModal');
            const title = document.getElementById('confirmModalTitle');
            const content = document.getElementById('confirmModalContent');
            const confirmBtn = document.getElementById('confirmModalConfirm');
            const cancelBtn = document.getElementById('confirmModalCancel');

            if (!modal) {
                console.error('Modal element not found');
                return;
            }

            title.textContent = options.title || 'Confirm';

            if (options.content) {
                content.innerHTML = options.content;
            } else {
                content.textContent = options.message || 'Are you sure?';
            }

            if (options.showConfirm !== false) {
                confirmBtn.style.display = 'inline-flex';
                confirmBtn.textContent = options.confirmText || 'Confirm';
                confirmBtn.onclick = options.onConfirm || closeModal;
            } else {
                confirmBtn.style.display = 'none';
            }

            if (options.showCancel !== false) {
                cancelBtn.style.display = 'inline-flex';
                cancelBtn.textContent = options.cancelText || 'Cancel';
                cancelBtn.onclick = options.onCancel || closeModal;
            } else {
                cancelBtn.style.display = 'none';
            }

            modal.classList.remove('hidden');
            modal.classList.add('flex');
        }

        function closeModal() {
            const modal = document.getElementById('confirmModal');
            if (modal) {
                modal.classList.add('hidden');
                modal.classList.remove('flex');
            }
        }

        // Show advanced print options
        function showPrintOptions(receiptId) {
            const modalHtml = `
                <div class="space-y-4">
                    <p class="text-gray-600 dark:text-gray-400">Choose your preferred receipt format:</p>

                    <div class="grid grid-cols-1 gap-3">
                        <button onclick="printReceipt(${receiptId}, false); closeModal()"
                                class="flex items-center p-4 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/40 rounded-lg border border-blue-200 dark:border-blue-700 transition-colors">
                            <i class="fas fa-desktop text-blue-600 text-xl mr-3"></i>
                            <div class="text-left">
                                <div class="font-medium text-blue-900 dark:text-blue-300">Regular Printer</div>
                                <div class="text-sm text-blue-700 dark:text-blue-400">Standard 8.5x11" format</div>
                            </div>
                        </button>

                        <button onclick="printReceipt(${receiptId}, true, 'html'); closeModal()"
                                class="flex items-center p-4 bg-green-50 hover:bg-green-100 dark:bg-green-900/20 dark:hover:bg-green-900/40 rounded-lg border border-green-200 dark:border-green-700 transition-colors">
                            <i class="fas fa-receipt text-green-600 text-xl mr-3"></i>
                            <div class="text-left">
                                <div class="font-medium text-green-900 dark:text-green-300">Cool Thermal Receipt</div>
                                <div class="text-sm text-green-700 dark:text-green-400">80mm with neon design & animations</div>
                            </div>
                        </button>

                        <button onclick="printReceipt(${receiptId}, true, 'text'); closeModal()"
                                class="flex items-center p-4 bg-purple-50 hover:bg-purple-100 dark:bg-purple-900/20 dark:hover:bg-purple-900/40 rounded-lg border border-purple-200 dark:border-purple-700 transition-colors">
                            <i class="fas fa-file-alt text-purple-600 text-xl mr-3"></i>
                            <div class="text-left">
                                <div class="font-medium text-purple-900 dark:text-purple-300">Plain Text Thermal</div>
                                <div class="text-sm text-purple-700 dark:text-purple-400">Simple ASCII art format</div>
                            </div>
                        </button>
                    </div>
                </div>
            `;

            showModal({
                title: '🖨️ Print Receipt Options',
                content: modalHtml,
                showCancel: true,
                cancelText: 'Cancel',
                showConfirm: false
            });
        }

        // Download receipt as PDF
        function downloadReceipt(receiptId) {
            const currentPath = window.location.pathname;
            const isInPagesDir = currentPath.includes('/pages/');
            const apiPath = isInPagesDir ? '../api/download-receipt.php' : 'api/download-receipt.php';

            window.open(`${apiPath}?id=${receiptId}`, '_blank');
        }

        // Show receipt modal
        function showReceiptModal(receipt) {
            let modal = document.getElementById('receiptModal');
            if (!modal) {
                createReceiptModal();
                modal = document.getElementById('receiptModal');
            }

            if (!modal) {
                console.error('Failed to create receipt modal');
                alert('Error displaying receipt modal');
                return;
            }

            // Populate modal with receipt data
            populateReceiptModal(receipt);

            // Show modal
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // Close receipt modal
        function closeReceiptModal() {
            document.getElementById('receiptModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Create receipt modal if it doesn't exist
        function createReceiptModal() {
            const modalHTML = `
                <div id="receiptModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
                    <div class="flex items-center justify-center min-h-screen p-4">
                        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                            <!-- Modal Header -->
                            <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                                    <i class="fas fa-receipt mr-2 text-blue-600"></i>
                                    Payment Receipt
                                </h3>
                                <button onclick="closeReceiptModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>

                            <!-- Modal Body -->
                            <div id="receiptContent" class="p-6">
                                <!-- Receipt content will be populated here -->
                            </div>

                            <!-- Modal Footer -->
                            <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 rounded-b-2xl">
                                <button onclick="downloadReceipt(window.currentReceiptId)"
                                        class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition duration-200">
                                    <i class="fas fa-download mr-2"></i>
                                    Download PDF
                                </button>
                                <button onclick="showPrintOptions(window.currentReceiptId)"
                                        class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                                    <i class="fas fa-print mr-2"></i>
                                    Print Options
                                </button>
                                <button onclick="closeReceiptModal()"
                                        class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition duration-200">
                                    <i class="fas fa-times mr-2"></i>
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // Close modal when clicking outside
            document.getElementById('receiptModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeReceiptModal();
                }
            });
        }

        // Populate receipt modal with data
        function populateReceiptModal(receipt) {
            window.currentReceiptId = receipt.id;

            const content = `
                <!-- Receipt Header -->
                <div class="text-center mb-6 pb-6 border-b-2 border-gray-200 dark:border-gray-600">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">${receipt.gym_name}</h2>
                    ${receipt.gym_phone ? `<p class="text-sm text-gray-600 dark:text-gray-400">Tel: ${receipt.gym_phone}</p>` : ''}
                    ${receipt.gym_email ? `<p class="text-sm text-gray-600 dark:text-gray-400">${receipt.gym_email}</p>` : ''}

                    <div class="mt-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">PAYMENT RECEIPT</h3>
                        <p class="text-xl font-bold text-blue-600 mt-2">Receipt #${receipt.receipt_number}</p>
                    </div>
                </div>

                <!-- Receipt Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Member Information -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                            <i class="fas fa-user mr-2 text-blue-600"></i>
                            Member Information
                        </h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Name:</span>
                                <span class="font-medium text-gray-900 dark:text-white">${receipt.member_name}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Member ID:</span>
                                <span class="font-medium text-gray-900 dark:text-white">${receipt.member_id}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Membership Details -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                            <i class="fas fa-dumbbell mr-2 text-blue-600"></i>
                            Membership Details
                        </h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Plan:</span>
                                <span class="font-medium text-gray-900 dark:text-white">${receipt.plan_name}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Duration:</span>
                                <span class="font-medium text-gray-900 dark:text-white">${receipt.duration_months} month(s)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Start Date:</span>
                                <span class="font-medium text-gray-900 dark:text-white">${new Date(receipt.start_date).toLocaleDateString()}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">End Date:</span>
                                <span class="font-medium text-gray-900 dark:text-white">${new Date(receipt.end_date).toLocaleDateString()}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Information -->
                <div class="bg-blue-50 dark:bg-blue-900 rounded-lg p-4 mb-6">
                    <h4 class="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                        <i class="fas fa-credit-card mr-2 text-blue-600"></i>
                        Payment Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Payment Date:</span>
                            <span class="font-medium text-gray-900 dark:text-white">${new Date(receipt.payment_date).toLocaleDateString()}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Method:</span>
                            <span class="font-medium text-gray-900 dark:text-white">${receipt.payment_method.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                        </div>
                        ${receipt.transaction_id ? `
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Transaction ID:</span>
                            <span class="font-medium text-gray-900 dark:text-white">${receipt.transaction_id}</span>
                        </div>` : ''}
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Processed By:</span>
                            <span class="font-medium text-gray-900 dark:text-white">${receipt.processed_by_name || 'Local Admin'}</span>
                        </div>
                    </div>
                </div>

                <!-- Total Amount -->
                <div class="bg-green-50 dark:bg-green-900 border-2 border-green-200 dark:border-green-700 rounded-lg p-6 text-center">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-semibold text-gray-700 dark:text-gray-300">Total Amount Paid:</span>
                        <span class="text-3xl font-bold text-green-600 dark:text-green-400">${receipt.currency_symbol}${parseFloat(receipt.amount).toFixed(2)}</span>
                    </div>
                </div>

                ${receipt.notes && !receipt.notes.includes('Local Admin') ? `
                <div class="mt-4 bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                    <h5 class="font-medium text-yellow-800 dark:text-yellow-200 mb-2">Notes:</h5>
                    <p class="text-yellow-700 dark:text-yellow-300 text-sm">${receipt.notes.replace(' (Local Admin)', '')}</p>
                </div>` : ''}

                <!-- Footer -->
                <div class="text-center mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
                    <p class="text-sm text-gray-600 dark:text-gray-400">Thank you for your membership!</p>
                    <p class="text-xs text-gray-500 dark:text-gray-500 mt-2">
                        Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
                    </p>
                </div>
            `;

            document.getElementById('receiptContent').innerHTML = content;
        }
        
        // Export functionality
        function exportData(type, format = 'csv') {
            const url = `api/export.php?type=${type}&format=${format}`;
            const link = document.createElement('a');
            link.href = url;
            link.download = `${type}_export_${new Date().toISOString().split('T')[0]}.${format}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        // Image preview
        function previewImage(input, previewId) {
            const file = input.files[0];
            const preview = document.getElementById(previewId);
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        }
        
        // Initialize tooltips (if using a tooltip library)
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize any tooltips here
        });
    </script>
    
    <!-- Page-specific scripts can be added here -->
    <?php if (isset($additionalScripts)): ?>
        <?= $additionalScripts ?>
    <?php endif; ?>

    <!-- Confirm Modal -->
    <div id="confirmModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full mx-4">
            <div class="p-6">
                <h3 id="confirmModalTitle" class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Confirm Action
                </h3>
                <div id="confirmModalContent" class="text-gray-600 dark:text-gray-400 mb-6">
                    Are you sure you want to proceed?
                </div>
                <div class="flex justify-end space-x-3">
                    <button id="confirmModalCancel"
                            onclick="closeModal()"
                            class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 font-medium rounded-lg transition duration-200">
                        Cancel
                    </button>
                    <button id="confirmModalConfirm"
                            onclick="closeModal()"
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                        Confirm
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
