# Protect backup files from direct access
# Only allow access from authenticated users

<Files "*.sql">
    Order Deny,Allow
    Deny from all
    # Allow access only from localhost and authenticated sessions
    Allow from 127.0.0.1
    Allow from ::1
</Files>

# Prevent directory browsing
Options -Indexes

# Additional security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(bak|backup|old|tmp|temp)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>
