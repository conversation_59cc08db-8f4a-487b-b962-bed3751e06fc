<?php
/**
 * Simple Backup System
 * MyGym Management System
 * 
 * A simple, reliable backup and restore system
 */

class SimpleBackup {
    private $backupDir;
    private $db;
    
    public function __construct() {
        $this->backupDir = __DIR__ . '/../backups';
        $this->db = Database::getInstance();
        
        // Create backup directory if it doesn't exist
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }
    
    /**
     * Create a database backup
     */
    public function createBackup($description = '') {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            $filename = "mygym_backup_{$timestamp}.sql";
            $filepath = $this->backupDir . '/' . $filename;
            
            // Get database configuration
            $config = require __DIR__ . '/../config/database.php';
            $dbname = $config['dbname'];
            
            // Start building SQL dump
            $sql = "-- MyGym Database Backup\n";
            $sql .= "-- Created: " . date('Y-m-d H:i:s') . "\n";
            $sql .= "-- Description: " . ($description ?: 'Manual backup') . "\n\n";
            
            $sql .= "SET FOREIGN_KEY_CHECKS = 0;\n";
            $sql .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n\n";
            
            // Get all tables
            $tables = $this->db->fetchAll("SHOW TABLES");
            $tableColumn = "Tables_in_{$dbname}";
            
            foreach ($tables as $table) {
                $tableName = $table[$tableColumn];
                
                // Get table structure
                $createTable = $this->db->fetch("SHOW CREATE TABLE `{$tableName}`");
                $sql .= "-- Table: {$tableName}\n";
                $sql .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
                $sql .= $createTable['Create Table'] . ";\n\n";
                
                // Get table data
                $rows = $this->db->fetchAll("SELECT * FROM `{$tableName}`");
                if (!empty($rows)) {
                    $sql .= "-- Data for table {$tableName}\n";
                    
                    foreach ($rows as $row) {
                        $values = [];
                        foreach ($row as $value) {
                            if ($value === null) {
                                $values[] = 'NULL';
                            } else {
                                $values[] = "'" . addslashes($value) . "'";
                            }
                        }
                        $sql .= "INSERT INTO `{$tableName}` VALUES (" . implode(', ', $values) . ");\n";
                    }
                    $sql .= "\n";
                }
            }
            
            $sql .= "SET FOREIGN_KEY_CHECKS = 1;\n";
            
            // Write to file
            if (file_put_contents($filepath, $sql) === false) {
                throw new Exception('Failed to write backup file');
            }
            
            return [
                'success' => true,
                'message' => 'Backup created successfully',
                'filename' => $filename,
                'size' => $this->formatFileSize(filesize($filepath))
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Backup failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Restore database from backup
     */
    public function restoreBackup($filename) {
        try {
            $filepath = $this->backupDir . '/' . $filename;
            
            if (!file_exists($filepath)) {
                throw new Exception('Backup file not found');
            }
            
            // Read SQL file
            $sql = file_get_contents($filepath);
            if ($sql === false) {
                throw new Exception('Failed to read backup file');
            }
            
            // Split into statements and execute
            $statements = explode(';', $sql);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && !preg_match('/^--/', $statement)) {
                    $this->db->query($statement);
                }
            }
            
            return [
                'success' => true,
                'message' => 'Database restored successfully'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Restore failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get list of backup files
     */
    public function getBackupList() {
        $backups = [];
        $files = glob($this->backupDir . '/mygym_backup_*.sql');
        
        foreach ($files as $file) {
            $filename = basename($file);
            $backups[] = [
                'filename' => $filename,
                'size' => $this->formatFileSize(filesize($file)),
                'date' => date('M j, Y g:i A', filemtime($file)),
                'timestamp' => filemtime($file)
            ];
        }
        
        // Sort by timestamp (newest first)
        usort($backups, function($a, $b) {
            return $b['timestamp'] - $a['timestamp'];
        });
        
        return $backups;
    }
    
    /**
     * Delete a backup file
     */
    public function deleteBackup($filename) {
        try {
            $filepath = $this->backupDir . '/' . $filename;
            
            if (!file_exists($filepath)) {
                throw new Exception('Backup file not found');
            }
            
            if (!unlink($filepath)) {
                throw new Exception('Failed to delete backup file');
            }
            
            return [
                'success' => true,
                'message' => 'Backup deleted successfully'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Delete failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Upload a backup file
     */
    public function uploadBackup($file) {
        try {
            if ($file['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('File upload error');
            }

            if ($file['size'] > 50 * 1024 * 1024) { // 50MB limit
                throw new Exception('File too large (max 50MB)');
            }

            $filename = $file['name'];
            if (!preg_match('/\.sql$/i', $filename)) {
                throw new Exception('Only .sql files are allowed');
            }

            // Generate safe filename
            $timestamp = date('Y-m-d_H-i-s');
            $safeFilename = "mygym_backup_uploaded_{$timestamp}.sql";
            $targetPath = $this->backupDir . '/' . $safeFilename;

            if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
                throw new Exception('Failed to save uploaded file');
            }

            return [
                'success' => true,
                'message' => 'Backup uploaded successfully',
                'filename' => $safeFilename,
                'size' => $this->formatFileSize(filesize($targetPath))
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Upload failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Download a backup file
     */
    public function downloadBackup($filename) {
        $filepath = $this->backupDir . '/' . $filename;

        if (!file_exists($filepath)) {
            return false;
        }

        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($filepath));

        readfile($filepath);
        return true;
    }
    
    /**
     * Get backup directory info
     */
    public function getBackupInfo() {
        $files = glob($this->backupDir . '/mygym_backup_*.sql');
        $totalSize = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
        }
        
        return [
            'count' => count($files),
            'total_size' => $this->formatFileSize($totalSize),
            'directory' => $this->backupDir
        ];
    }
    
    /**
     * Format file size
     */
    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
