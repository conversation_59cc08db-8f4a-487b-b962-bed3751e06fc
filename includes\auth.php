<?php
/**
 * Authentication and Authorization Helper
 * MyGym Management System
 */

// Include required classes
require_once __DIR__ . '/Config.php';
require_once __DIR__ . '/ActivityLogger.php';

class Auth {
    
    /**
     * Check if user is logged in
     */
    public static function check() {
        return isset($_SESSION['user_id']);
    }
    
    /**
     * Get current user data
     */
    public static function user() {
        if (!self::check()) {
            return null;
        }

        $user = [
            'id' => $_SESSION['user_id'],
            'name' => $_SESSION['user_name'],
            'email' => $_SESSION['user_email'],
            'role' => $_SESSION['user_role'],
            'is_local_admin' => $_SESSION['is_local_admin'] ?? false
        ];

        if (self::isLocalAdmin()) {
            $user['local_admin_username'] = $_SESSION['local_admin_username'] ?? null;
        }

        return $user;
    }

    /**
     * Check if current user is a local admin
     */
    public static function isLocalAdmin() {
        return isset($_SESSION['is_local_admin']) && $_SESSION['is_local_admin'] === true;
    }
    
    /**
     * Get current user ID
     */
    public static function id() {
        return $_SESSION['user_id'] ?? null;
    }
    
    /**
     * Get current user role
     */
    public static function role() {
        return $_SESSION['user_role'] ?? null;
    }
    
    /**
     * Check if user has specific role
     */
    public static function hasRole($role) {
        return self::role() === $role;
    }
    
    /**
     * Check if user is admin
     */
    public static function isAdmin() {
        return self::hasRole('admin');
    }
    
    /**
     * Check if user is manager or admin
     */
    public static function isManager() {
        return in_array(self::role(), ['admin', 'manager']);
    }
    
    /**
     * Check if user can access specific feature
     */
    public static function can($permission) {
        $role = self::role();
        
        $permissions = [
            'admin' => [
                'users.create', 'users.edit', 'users.delete', 'users.view',
                'members.create', 'members.edit', 'members.delete', 'members.view',
                'trainers.create', 'trainers.edit', 'trainers.delete', 'trainers.view',
                'payments.create', 'payments.edit', 'payments.delete', 'payments.view',
                'plans.create', 'plans.edit', 'plans.delete', 'plans.view',
                'equipment.create', 'equipment.edit', 'equipment.delete', 'equipment.view',
                'reports.view', 'settings.edit', 'notifications.send',
                'campaigns.create', 'campaigns.edit', 'campaigns.delete'
            ],
            'manager' => [
                'members.create', 'members.edit', 'members.view',
                'trainers.create', 'trainers.edit', 'trainers.view',
                'payments.create', 'payments.edit', 'payments.view',
                'plans.view', 'equipment.create', 'equipment.edit', 'equipment.view',
                'reports.view', 'notifications.send'
            ],
            'receptionist' => [
                'members.create', 'members.edit', 'members.view',
                'payments.create', 'payments.view',
                'plans.view', 'equipment.view'
            ]
        ];
        
        return isset($permissions[$role]) && in_array($permission, $permissions[$role]);
    }
    
    /**
     * Require authentication
     */
    public static function requireAuth() {
        if (!self::check()) {
            header('Location: index.php');
            exit;
        }
    }
    
    /**
     * Require specific role
     */
    public static function requireRole($role) {
        self::requireAuth();
        
        if (!self::hasRole($role)) {
            header('Location: dashboard.php?error=access_denied');
            exit;
        }
    }
    
    /**
     * Require admin access
     */
    public static function requireAdmin() {
        self::requireRole('admin');
    }
    
    /**
     * Require manager or admin access
     */
    public static function requireManager() {
        self::requireAuth();
        
        if (!self::isManager()) {
            header('Location: dashboard.php?error=access_denied');
            exit;
        }
    }
    
    /**
     * Require specific permission
     */
    public static function requirePermission($permission) {
        self::requireAuth();
        
        if (!self::can($permission)) {
            header('Location: dashboard.php?error=access_denied');
            exit;
        }
    }
    
    /**
     * Logout user
     */
    public static function logout() {
        // Handle local admin logout
        if (self::isLocalAdmin()) {
            // Clear local admin remember tokens
            if (isset($_COOKIE['local_remember_token'])) {
                setcookie('local_remember_token', '', time() - 3600, '/');
            }
            if (isset($_COOKIE['local_remember_user'])) {
                setcookie('local_remember_user', '', time() - 3600, '/');
            }

            // Log activity for local admin
            try {
                if (class_exists('ActivityLogger')) {
                    ActivityLogger::log('Local Admin Logout', 'local_admin', $_SESSION['local_admin_username'] ?? 'unknown');
                }
            } catch (Exception $e) {
                // Ignore logging errors
            }
        } else {
            // Handle database user logout
            if (isset($_COOKIE['remember_token'])) {
                try {
                    $db = Database::getInstance();
                    $db->update('users', ['remember_token' => null], 'id = ?', [self::id()]);
                } catch (Exception $e) {
                    // Ignore database errors during logout
                }
                setcookie('remember_token', '', time() - 3600, '/');
            }

            // Log activity for database user
            try {
                if (class_exists('ActivityLogger')) {
                    ActivityLogger::log('User Logout');
                }
            } catch (Exception $e) {
                // Ignore logging errors
            }
        }

        // Clear session
        session_destroy();

        header('Location: index.php');
        exit;
    }
    
    /**
     * Generate CSRF token
     */
    public static function generateCsrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Verify CSRF token
     */
    public static function verifyCsrfToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Get CSRF token input field
     */
    public static function csrfField() {
        $token = self::generateCsrfToken();
        return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
    }
}

/**
 * Session management helper
 */
class Session {
    
    /**
     * Set flash message
     */
    public static function flash($key, $message) {
        $_SESSION['flash'][$key] = $message;
    }
    
    /**
     * Get and clear flash message
     */
    public static function getFlash($key) {
        $message = $_SESSION['flash'][$key] ?? null;
        unset($_SESSION['flash'][$key]);
        return $message;
    }
    
    /**
     * Check if flash message exists
     */
    public static function hasFlash($key) {
        return isset($_SESSION['flash'][$key]);
    }
    
    /**
     * Set success message
     */
    public static function success($message) {
        self::flash('success', $message);
    }
    
    /**
     * Set error message
     */
    public static function error($message) {
        self::flash('error', $message);
    }
    
    /**
     * Set warning message
     */
    public static function warning($message) {
        self::flash('warning', $message);
    }
    
    /**
     * Set info message
     */
    public static function info($message) {
        self::flash('info', $message);
    }
}

/**
 * Helper functions
 */

/**
 * Check if user is authenticated
 */
function auth() {
    return Auth::user();
}

/**
 * Get current user
 */
function user() {
    return Auth::user();
}

/**
 * Check if user can perform action
 */
function can($permission) {
    return Auth::can($permission);
}

/**
 * Redirect with message
 */
function redirect($url, $message = null, $type = 'info') {
    if ($message) {
        Session::flash($type, $message);
    }
    header("Location: $url");
    exit;
}

/**
 * Redirect back with message
 */
function back($message = null, $type = 'error') {
    $url = $_SERVER['HTTP_REFERER'] ?? 'dashboard.php';
    redirect($url, $message, $type);
}

/**
 * Generate secure random string
 */
function generateRandomString($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Sanitize input
 */
function sanitize($input) {
    if (is_array($input)) {
        return array_map('sanitize', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Format currency
 */
function formatCurrency($amount, $currency = null) {
    if ($currency === null) {
        $currency = Config::get('currency', 'USD');
    }
    
    $symbols = [
        'USD' => '$',
        'EUR' => '€',
        'GBP' => '£',
        'CAD' => 'C$',
        'AUD' => 'A$',
        'INR' => '₹',
        'JPY' => '¥'
    ];
    
    $symbol = $symbols[$currency] ?? $currency;
    return $symbol . number_format($amount ?? 0, 2);
}

/**
 * Format date
 */
function formatDate($date, $format = null) {
    if ($format === null) {
        $format = Config::get('date_format', 'Y-m-d');
    }
    
    if (is_string($date)) {
        $date = new DateTime($date);
    }
    
    return $date->format($format);
}

/**
 * Time ago helper
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    
    return floor($time/31536000) . ' years ago';
}
