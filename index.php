<?php
/**
 * MyGym Management System - Login Page
 * Modern responsive login with multi-language support
 */

session_start();

// Check if system is installed
if (!file_exists('config/config.php')) {
    header('Location: install.php');
    exit;
}

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit;
}

require_once 'config/database.php';
require_once 'includes/Config.php';

$error = '';
$success = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);

    if (empty($email) || empty($password)) {
        $error = 'Please enter both email and password.';
    } else {
        $loginSuccessful = false;

        // First, check local admin credentials
        if (file_exists('config/admin_credentials.php')) {
            $localAdmins = include 'config/admin_credentials.php';

            foreach ($localAdmins as $username => $adminData) {
                if ($adminData['email'] === $email &&
                    $adminData['is_active'] &&
                    $password === $adminData['password']) {

                    // Local admin login successful
                    $_SESSION['user_id'] = 'local_' . $username;
                    $_SESSION['user_name'] = $adminData['name'];
                    $_SESSION['user_email'] = $adminData['email'];
                    $_SESSION['user_role'] = $adminData['role'];
                    $_SESSION['is_local_admin'] = true;
                    $_SESSION['local_admin_username'] = $username;

                    // Handle remember me for local admin
                    if ($remember) {
                        $token = bin2hex(random_bytes(32));
                        $_SESSION['local_remember_token'] = $token;
                        setcookie('local_remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 days
                        setcookie('local_remember_user', $username, time() + (30 * 24 * 60 * 60), '/');
                    }

                    // Log activity (without database dependency)
                    try {
                        if (class_exists('ActivityLogger')) {
                            ActivityLogger::log('Local Admin Login', 'local_admin', $username);
                        }
                    } catch (Exception $e) {
                        // Ignore logging errors for local admin
                    }

                    $loginSuccessful = true;
                    break;
                }
            }
        }

        // If local admin login failed, try database authentication
        if (!$loginSuccessful) {
            try {
                $db = Database::getInstance();
                $user = $db->fetch(
                    "SELECT id, name, email, password, role, is_active FROM users WHERE email = ? AND is_active = 1",
                    [$email]
                );

                if ($user && password_verify($password, $user['password'])) {
                    // Database user login successful
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_name'] = $user['name'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_role'] = $user['role'];
                    $_SESSION['is_local_admin'] = false;

                    // Update last login
                    $db->update('users', ['last_login' => date('Y-m-d H:i:s')], 'id = ?', [$user['id']]);

                    // Handle remember me
                    if ($remember) {
                        $token = bin2hex(random_bytes(32));
                        $db->update('users', ['remember_token' => $token], 'id = ?', [$user['id']]);
                        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 days
                    }

                    // Log activity
                    ActivityLogger::log('User Login', 'users', $user['id']);

                    $loginSuccessful = true;
                }
            } catch (Exception $e) {
                $error = 'Login failed. Please try again.';
            }
        }

        if ($loginSuccessful) {
            header('Location: dashboard.php');
            exit;
        } else {
            $error = 'Invalid email or password.';
        }
    }
}

// Check remember me tokens
if (!isset($_SESSION['user_id'])) {
    // Check local admin remember token first
    if (isset($_COOKIE['local_remember_token']) && isset($_COOKIE['local_remember_user'])) {
        if (file_exists('config/admin_credentials.php')) {
            $localAdmins = include 'config/admin_credentials.php';
            $username = $_COOKIE['local_remember_user'];

            if (isset($localAdmins[$username]) && $localAdmins[$username]['is_active']) {
                $adminData = $localAdmins[$username];

                // Restore local admin session
                $_SESSION['user_id'] = 'local_' . $username;
                $_SESSION['user_name'] = $adminData['name'];
                $_SESSION['user_email'] = $adminData['email'];
                $_SESSION['user_role'] = $adminData['role'];
                $_SESSION['is_local_admin'] = true;
                $_SESSION['local_admin_username'] = $username;
                $_SESSION['local_remember_token'] = $_COOKIE['local_remember_token'];

                header('Location: dashboard.php');
                exit;
            } else {
                // Clear invalid local admin cookies
                setcookie('local_remember_token', '', time() - 3600, '/');
                setcookie('local_remember_user', '', time() - 3600, '/');
            }
        }
    }

    // Check database user remember token
    if (isset($_COOKIE['remember_token'])) {
        try {
            $db = Database::getInstance();
            $user = $db->fetch(
                "SELECT id, name, email, role FROM users WHERE remember_token = ? AND is_active = 1",
                [$_COOKIE['remember_token']]
            );

            if ($user) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_name'] = $user['name'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['is_local_admin'] = false;

                header('Location: dashboard.php');
                exit;
            }
        } catch (Exception $e) {
            // Clear invalid cookie
            setcookie('remember_token', '', time() - 3600, '/');
        }
    }
}

// Get gym name for display
$gymName = 'MyGym';
try {
    $gymName = Config::get('gym_name', 'MyGym');
} catch (Exception $e) {
    // Config not available yet
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?= htmlspecialchars($gymName) ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { 
            font-family: 'Inter', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-10 rounded-full floating-animation"></div>
        <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-white opacity-5 rounded-full floating-animation" style="animation-delay: -3s;"></div>
    </div>
    
    <div class="relative w-full max-w-md">
        <!-- Login Card -->
        <div class="login-card rounded-2xl shadow-2xl p-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-4">
                    <i class="fas fa-dumbbell text-white text-2xl"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-900 mb-2"><?= htmlspecialchars($gymName) ?></h1>
                <p class="text-gray-600">Sign in to your account</p>
            </div>
            
            <!-- Error/Success Messages -->
            <?php if ($error): ?>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <i class="fas fa-exclamation-circle text-red-400 mt-0.5 mr-3"></i>
                        <span class="text-red-800"><?= htmlspecialchars($error) ?></span>
                    </div>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <i class="fas fa-check-circle text-green-400 mt-0.5 mr-3"></i>
                        <span class="text-green-800"><?= htmlspecialchars($success) ?></span>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Login Form -->
            <form method="POST" class="space-y-6">
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-envelope mr-2"></i>Email Address
                    </label>
                    <input type="email" 
                           id="email" 
                           name="email" 
                           value="<?= htmlspecialchars($_POST['email'] ?? '') ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200"
                           placeholder="Enter your email"
                           required>
                </div>
                
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-lock mr-2"></i>Password
                    </label>
                    <div class="relative">
                        <input type="password" 
                               id="password" 
                               name="password" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 pr-12"
                               placeholder="Enter your password"
                               required>
                        <button type="button" 
                                onclick="togglePassword()"
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                            <i id="password-icon" class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" 
                               name="remember" 
                               class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-600">Remember me</span>
                    </label>
                    
                    <a href="forgot-password.php" class="text-sm text-blue-600 hover:text-blue-800 transition duration-200">
                        Forgot password?
                    </a>
                </div>
                
                <button type="submit" 
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In
                </button>
            </form>
            
            <!-- Language Switcher -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="flex items-center justify-center space-x-4 text-sm">
                    <span class="text-gray-500">Language:</span>
                    <select class="border border-gray-300 rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="en">English</option>
                        <option value="fr">Français</option>
                        <option value="es">Español</option>
                        <option value="ar">العربية</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="text-center mt-8 text-white text-sm">
            <p>&copy; <?= date('Y') ?> <?= htmlspecialchars($gymName) ?>. All rights reserved.</p>
            <p class="mt-1 opacity-75">Powered by MyGym Management System</p>
            <div class="mt-3 pt-3 border-t border-white border-opacity-20">
                <a href="restore.php" class="text-white hover:text-blue-200 transition-colors text-xs">
                    <i class="fas fa-database mr-1"></i>Restore Database
                </a>
            </div>
        </div>
    </div>
    
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordIcon = document.getElementById('password-icon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordIcon.className = 'fas fa-eye';
            }
        }
        
        // Auto-focus email field
        document.getElementById('email').focus();
        
        // Add loading state to form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            const button = this.querySelector('button[type="submit"]');
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Signing In...';
            button.disabled = true;
        });
    </script>
</body>
</html>
