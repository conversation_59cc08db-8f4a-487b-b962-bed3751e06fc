<?php
/**
 * Reset Admin Password
 * Use this if you can't login with your installation credentials
 */

// Check if config exists
if (!file_exists('config/config.php')) {
    die('System not installed. Please run install.php first.');
}

require_once 'config/database.php';

echo "<h1>Reset Admin Password</h1>\n";

if ($_POST['reset_password'] ?? false) {
    $email = trim($_POST['email'] ?? '');
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    $errors = [];
    
    if (empty($email)) $errors[] = 'Email is required';
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'Valid email is required';
    if (empty($newPassword)) $errors[] = 'New password is required';
    if (strlen($newPassword) < 6) $errors[] = 'Password must be at least 6 characters';
    if ($newPassword !== $confirmPassword) $errors[] = 'Passwords do not match';
    
    if (empty($errors)) {
        try {
            $db = Database::getInstance();
            
            // Check if user exists
            $user = $db->fetch("SELECT id, name FROM users WHERE email = ?", [$email]);
            
            if (!$user) {
                $errors[] = 'No user found with that email address';
            } else {
                // Update password
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                $db->update('users', ['password' => $hashedPassword], 'id = ?', [$user['id']]);
                
                echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
                echo "<h3>✅ Password Reset Successful!</h3>\n";
                echo "<p>Password has been reset for: <strong>{$user['name']}</strong> ({$email})</p>\n";
                echo "<p>You can now login with your new password.</p>\n";
                echo "<p><a href='index.php' style='color: #155724; font-weight: bold;'>→ Go to Login Page</a></p>\n";
                echo "</div>\n";
                
                // Don't show the form again
                $hideForm = true;
            }
        } catch (Exception $e) {
            $errors[] = 'Database error: ' . $e->getMessage();
        }
    }
    
    if (!empty($errors)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<h4>Errors:</h4>\n";
        echo "<ul>\n";
        foreach ($errors as $error) {
            echo "<li>{$error}</li>\n";
        }
        echo "</ul>\n";
        echo "</div>\n";
    }
}

if (!isset($hideForm)) {
    try {
        $db = Database::getInstance();
        
        // Show existing users
        echo "<h3>Existing Admin Users:</h3>\n";
        $users = $db->fetchAll("SELECT id, name, email, role, is_active FROM users WHERE role = 'admin'");
        
        if (empty($users)) {
            echo "<p style='color: #dc3545;'>❌ No admin users found. You may need to reinstall the system.</p>\n";
        } else {
            echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>\n";
            echo "<tr style='background: #f8f9fa;'>\n";
            echo "<th style='border: 1px solid #dee2e6; padding: 8px; text-align: left;'>Name</th>\n";
            echo "<th style='border: 1px solid #dee2e6; padding: 8px; text-align: left;'>Email</th>\n";
            echo "<th style='border: 1px solid #dee2e6; padding: 8px; text-align: left;'>Status</th>\n";
            echo "</tr>\n";
            
            foreach ($users as $user) {
                $status = $user['is_active'] ? '✅ Active' : '❌ Inactive';
                echo "<tr>\n";
                echo "<td style='border: 1px solid #dee2e6; padding: 8px;'>{$user['name']}</td>\n";
                echo "<td style='border: 1px solid #dee2e6; padding: 8px;'>{$user['email']}</td>\n";
                echo "<td style='border: 1px solid #dee2e6; padding: 8px;'>{$status}</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
        
        echo "<h3>Reset Password:</h3>\n";
        echo '<form method="post" style="margin: 20px 0; padding: 20px; border: 1px solid #ccc; background: #f9f9f9; border-radius: 5px;">
            <div style="margin-bottom: 15px;">
                <label for="email" style="display: block; margin-bottom: 5px; font-weight: bold;">Email Address:</label>
                <input type="email" id="email" name="email" required style="width: 100%; max-width: 300px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;" placeholder="<EMAIL>">
                <small style="color: #666;">Enter the email address of the admin account</small>
            </div>
            
            <div style="margin-bottom: 15px;">
                <label for="new_password" style="display: block; margin-bottom: 5px; font-weight: bold;">New Password:</label>
                <input type="password" id="new_password" name="new_password" required minlength="6" style="width: 100%; max-width: 300px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;" placeholder="Enter new password">
                <small style="color: #666;">Minimum 6 characters</small>
            </div>
            
            <div style="margin-bottom: 20px;">
                <label for="confirm_password" style="display: block; margin-bottom: 5px; font-weight: bold;">Confirm Password:</label>
                <input type="password" id="confirm_password" name="confirm_password" required minlength="6" style="width: 100%; max-width: 300px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;" placeholder="Confirm new password">
            </div>
            
            <button type="submit" name="reset_password" value="1" style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">Reset Password</button>
        </form>';
        
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<h4>⚠️ Security Notice:</h4>\n";
        echo "<p>This script allows resetting admin passwords without authentication. For security:</p>\n";
        echo "<ul>\n";
        echo "<li>Delete this file after use</li>\n";
        echo "<li>Only use this on your own server</li>\n";
        echo "<li>Make sure your new password is strong</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
    } catch (Exception $e) {
        echo "<p style='color: #dc3545;'>❌ Database error: " . $e->getMessage() . "</p>\n";
    }
}

echo "<hr>\n";
echo "<p><a href='debug_login.php'>Debug Login Issues</a> | <a href='index.php'>← Back to Login</a></p>\n";
