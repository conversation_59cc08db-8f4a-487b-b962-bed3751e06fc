﻿<?php
/**
 * Email Service Class with PHPMailer Support
 * MyGym Management System
 * 
 * Handles email sending using PHPMailer for proper SMTP support
 */

require_once __DIR__ . '/Config.php';
require_once __DIR__ . '/EmailTemplates.php';
require_once __DIR__ . '/phpmailer/PHPMailer.php';
require_once __DIR__ . '/phpmailer/SMTP.php';
require_once __DIR__ . '/phpmailer/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class EmailService {
    private $from_email;
    private $from_name;
    private $smtp_host;
    private $smtp_port;
    private $smtp_user;
    private $smtp_pass;
    private $smtp_encryption;

    public function __construct() {
        // Load email settings from config
        $this->from_email = Config::get('gym_email') ?: Config::get('smtp_user');
        $this->from_name = Config::get('gym_name') ?: 'MyGym';
        $this->smtp_host = Config::get('smtp_host');
        $this->smtp_port = (int)Config::get('smtp_port') ?: 587;
        $this->smtp_user = Config::get('smtp_user');
        $this->smtp_pass = Config::get('smtp_pass');
        $this->smtp_encryption = Config::get('smtp_encryption') ?: 'tls';
    }

    /**
     * Check if email is properly configured
     */
    public function isConfigured() {
        return !empty($this->smtp_host) && 
               !empty($this->smtp_user) && 
               !empty($this->smtp_pass) && 
               !empty($this->from_email);
    }

    /**
     * Send email using PHPMailer with SMTP
     */
    public function sendEmail($to_email, $to_name, $subject, $message, $isHTML = true) {
        if (!$this->isConfigured()) {
            return [
                'success' => false, 
                'message' => 'Email not configured. Please configure SMTP settings in Settings page.'
            ];
        }

        try {
            // Create PHPMailer instance
            $mail = new PHPMailer(true);

            // Server settings
            $mail->isSMTP();
            $mail->Host = $this->smtp_host;
            $mail->SMTPAuth = true;
            $mail->Username = $this->smtp_user;
            $mail->Password = $this->smtp_pass;
            $mail->Port = $this->smtp_port;

            // Set encryption
            if ($this->smtp_encryption === 'ssl') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
            } elseif ($this->smtp_encryption === 'tls') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            }

            // Recipients
            $mail->setFrom($this->from_email, $this->from_name);
            $mail->addAddress($to_email, $to_name);

            // Content
            $mail->isHTML($isHTML);
            $mail->Subject = $subject;
            
            if ($isHTML) {
                $mail->Body = $this->formatHTMLMessage($message);
                $mail->AltBody = strip_tags($message);
            } else {
                $mail->Body = $message;
            }

            // Send email
            $mail->send();
            
            return [
                'success' => true,
                'message' => 'Email sent successfully'
            ];

        } catch (Exception $e) {
            error_log('PHPMailer Error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to send email: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Format HTML message with gym branding
     */
    private function formatHTMLMessage($message) {
        $gymName = htmlspecialchars($this->from_name);
        $gymEmail = htmlspecialchars($this->from_email);
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>{$gymName}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #3b82f6; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
                .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
                .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <div class='logo'>{$gymName}</div>
                    <p style='margin: 0; opacity: 0.9;'>Fitness Management System</p>
                </div>
                <div class='content'>
                    {$message}
                </div>
                <div class='footer'>
                    <p>This email was sent from {$gymName}</p>
                    <p>Contact: {$gymEmail}</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Test email configuration
     */
    public function testConfiguration() {
        if (!$this->isConfigured()) {
            return [
                'success' => false,
                'message' => 'Email configuration incomplete'
            ];
        }

        try {
            $mail = new PHPMailer(true);
            $mail->isSMTP();
            $mail->Host = $this->smtp_host;
            $mail->SMTPAuth = true;
            $mail->Username = $this->smtp_user;
            $mail->Password = $this->smtp_pass;
            $mail->Port = $this->smtp_port;

            if ($this->smtp_encryption === 'ssl') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
            } elseif ($this->smtp_encryption === 'tls') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            }

            // Test connection without sending
            $mail->SMTPDebug = 0;
            $mail->Timeout = 10;
            
            if ($mail->smtpConnect()) {
                $mail->smtpClose();
                return [
                    'success' => true,
                    'message' => 'SMTP connection successful'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to connect to SMTP server'
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'SMTP test failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send templated email
     */
    public function sendTemplatedEmail($to_email, $to_name, $templateType, $templateData = []) {
        if (!$this->isConfigured()) {
            return [
                'success' => false,
                'message' => 'Email not configured. Please configure SMTP settings in Settings page.'
            ];
        }

        try {
            // Generate template content
            $template = EmailTemplates::generateTemplate($templateType, $templateData);
            $subject = $template['subject'];
            $content = $template['content'];

            // Wrap content in full HTML template
            $htmlBody = EmailTemplates::wrapInTemplate($content, $subject);

            // Create PHPMailer instance
            $mail = new PHPMailer(true);

            // Server settings
            $mail->isSMTP();
            $mail->Host = $this->smtp_host;
            $mail->SMTPAuth = true;
            $mail->Username = $this->smtp_user;
            $mail->Password = $this->smtp_pass;
            $mail->Port = $this->smtp_port;

            // Set encryption
            if ($this->smtp_encryption === 'ssl') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
            } elseif ($this->smtp_encryption === 'tls') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            }

            // Recipients
            $mail->setFrom($this->from_email, $this->from_name);
            $mail->addAddress($to_email, $to_name);

            // Content
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $htmlBody;
            $mail->AltBody = strip_tags($content);

            // Send email
            $mail->send();

            return [
                'success' => true,
                'message' => 'Templated email sent successfully'
            ];

        } catch (Exception $e) {
            error_log('PHPMailer Template Error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to send templated email: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Test connection (alias for testConfiguration for backward compatibility)
     */
    public function testConnection() {
        return $this->testConfiguration();
    }

    /**
     * Get email configuration summary (for compatibility)
     */
    public function getConfigSummary() {
        return [
            'configured' => $this->isConfigured(),
            'from_email' => $this->from_email,
            'from_name' => $this->from_name,
            'smtp_host' => $this->smtp_host,
            'smtp_port' => $this->smtp_port,
            'smtp_user' => $this->smtp_user,
            'smtp_encryption' => $this->smtp_encryption,
            'phpmailer_available' => class_exists('PHPMailer\PHPMailer\PHPMailer'),
            'mail_function_available' => function_exists('mail') // For backward compatibility
        ];
    }

    /**
     * Get current configuration status (detailed)
     */
    public function getConfigurationStatus() {
        return [
            'configured' => $this->isConfigured(),
            'smtp_host' => !empty($this->smtp_host),
            'smtp_user' => !empty($this->smtp_user),
            'smtp_pass' => !empty($this->smtp_pass),
            'from_email' => !empty($this->from_email),
            'settings' => [
                'host' => $this->smtp_host,
                'port' => $this->smtp_port,
                'user' => $this->smtp_user,
                'encryption' => $this->smtp_encryption,
                'from_email' => $this->from_email,
                'from_name' => $this->from_name
            ]
        ];
    }
}
