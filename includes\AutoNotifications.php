<?php
/**
 * Automatic Notifications System
 * Handles welcome messages, payment confirmations, and membership expiry notifications
 * MyGym Management System
 */

require_once 'EmailService.php';
require_once 'SmsService.php';
require_once 'EmailTemplates.php';
require_once 'SmsTemplates.php';
require_once 'Config.php';

class AutoNotifications {
    
    private $emailService;
    private $smsService;
    
    public function __construct() {
        $this->emailService = new EmailService();
        $this->smsService = new SmsService();
    }
    
    /**
     * Send welcome notifications to new member
     */
    public function sendWelcomeNotifications($memberId) {
        global $db;
        
        try {
            // Get member details
            $member = $db->fetch("
                SELECT m.*, p.name as plan_name, p.duration_months 
                FROM members m 
                LEFT JOIN plans p ON m.plan_id = p.id 
                WHERE m.id = ?
            ", [$memberId]);
            
            if (!$member) {
                error_log("AutoNotifications: Member not found for ID: $memberId");
                return false;
            }
            
            $memberName = trim($member['first_name'] . ' ' . $member['last_name']);
            
            // Prepare template data
            $templateData = [
                'member_name' => $memberName,
                'member_id' => $member['member_id'],
                'plan_name' => $member['plan_name'] ?? 'Standard Plan',
                'start_date' => $member['start_date'] ? date('F j, Y', strtotime($member['start_date'])) : date('F j, Y'),
                'end_date' => $member['end_date'] ? date('F j, Y', strtotime($member['end_date'])) : 'N/A'
            ];
            
            $results = [];
            
            // Send welcome email
            if (!empty($member['email']) && $this->emailService->isConfigured()) {
                try {
                    $emailResult = $this->emailService->sendTemplatedEmail(
                        $member['email'],
                        $memberName,
                        'welcome',
                        $templateData
                    );
                    $results['email'] = $emailResult;
                    
                    if ($emailResult['success']) {
                        error_log("AutoNotifications: Welcome email sent to $memberName ({$member['email']})");
                    } else {
                        error_log("AutoNotifications: Welcome email failed for $memberName: " . $emailResult['message']);
                    }
                } catch (Exception $e) {
                    error_log("AutoNotifications: Welcome email exception for $memberName: " . $e->getMessage());
                    $results['email'] = ['success' => false, 'message' => $e->getMessage()];
                }
            }
            
            // Send welcome SMS
            if (!empty($member['phone']) && $this->smsService->isConfigured()) {
                try {
                    $smsResult = $this->smsService->sendTemplatedSms(
                        $member['phone'],
                        'welcome',
                        $templateData
                    );
                    $results['sms'] = $smsResult;
                    
                    if ($smsResult['success']) {
                        error_log("AutoNotifications: Welcome SMS sent to $memberName ({$member['phone']})");
                    } else {
                        error_log("AutoNotifications: Welcome SMS failed for $memberName: " . $smsResult['message']);
                    }
                } catch (Exception $e) {
                    error_log("AutoNotifications: Welcome SMS exception for $memberName: " . $e->getMessage());
                    $results['sms'] = ['success' => false, 'message' => $e->getMessage()];
                }
            }
            
            return $results;
            
        } catch (Exception $e) {
            error_log("AutoNotifications: Welcome notifications failed for member $memberId: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send payment confirmation notifications
     */
    public function sendPaymentConfirmationNotifications($paymentId) {
        global $db;
        
        try {
            // Get payment and member details
            $payment = $db->fetch("
                SELECT p.*, m.first_name, m.last_name, m.email, m.phone, m.member_id,
                       pl.name as plan_name, pl.duration_months
                FROM payments p
                JOIN members m ON p.member_id = m.id
                LEFT JOIN plans pl ON p.plan_id = pl.id
                WHERE p.id = ?
            ", [$paymentId]);
            
            if (!$payment) {
                error_log("AutoNotifications: Payment not found for ID: $paymentId");
                return false;
            }
            
            $memberName = trim($payment['first_name'] . ' ' . $payment['last_name']);
            
            // Prepare template data
            $templateData = [
                'member_name' => $memberName,
                'member_id' => $payment['member_id'],
                'amount' => number_format($payment['amount'], 2),
                'receipt_number' => $payment['receipt_number'],
                'payment_date' => date('F j, Y', strtotime($payment['payment_date'])),
                'payment_method' => ucfirst($payment['payment_method']),
                'plan_name' => $payment['plan_name'] ?? 'Standard Plan',
                'start_date' => $payment['start_date'] ? date('F j, Y', strtotime($payment['start_date'])) : 'N/A',
                'end_date' => $payment['end_date'] ? date('F j, Y', strtotime($payment['end_date'])) : 'N/A'
            ];
            
            $results = [];
            
            // Send payment confirmation email
            if (!empty($payment['email']) && $this->emailService->isConfigured()) {
                try {
                    $emailResult = $this->emailService->sendTemplatedEmail(
                        $payment['email'],
                        $memberName,
                        'payment_confirmation',
                        $templateData
                    );
                    $results['email'] = $emailResult;
                    
                    if ($emailResult['success']) {
                        error_log("AutoNotifications: Payment confirmation email sent to $memberName ({$payment['email']})");
                    } else {
                        error_log("AutoNotifications: Payment confirmation email failed for $memberName: " . $emailResult['message']);
                    }
                } catch (Exception $e) {
                    error_log("AutoNotifications: Payment confirmation email exception for $memberName: " . $e->getMessage());
                    $results['email'] = ['success' => false, 'message' => $e->getMessage()];
                }
            }
            
            // Send payment confirmation SMS
            if (!empty($payment['phone']) && $this->smsService->isConfigured()) {
                try {
                    $smsResult = $this->smsService->sendTemplatedSms(
                        $payment['phone'],
                        'payment_confirmation',
                        $templateData
                    );
                    $results['sms'] = $smsResult;
                    
                    if ($smsResult['success']) {
                        error_log("AutoNotifications: Payment confirmation SMS sent to $memberName ({$payment['phone']})");
                    } else {
                        error_log("AutoNotifications: Payment confirmation SMS failed for $memberName: " . $smsResult['message']);
                    }
                } catch (Exception $e) {
                    error_log("AutoNotifications: Payment confirmation SMS exception for $memberName: " . $e->getMessage());
                    $results['sms'] = ['success' => false, 'message' => $e->getMessage()];
                }
            }
            
            return $results;
            
        } catch (Exception $e) {
            error_log("AutoNotifications: Payment confirmation notifications failed for payment $paymentId: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send expired membership notifications
     */
    public function sendExpiredMembershipNotifications() {
        global $db;
        
        try {
            // Get members with expired memberships (expired yesterday to avoid spam)
            $expiredMembers = $db->fetchAll("
                SELECT m.*, p.name as plan_name 
                FROM members m 
                LEFT JOIN plans p ON m.plan_id = p.id 
                WHERE m.status = 'active' 
                AND m.end_date = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                AND (m.email IS NOT NULL OR m.phone IS NOT NULL)
            ");
            
            if (empty($expiredMembers)) {
                return ['success' => true, 'message' => 'No expired memberships to notify'];
            }
            
            $results = [];
            $emailCount = 0;
            $smsCount = 0;
            
            foreach ($expiredMembers as $member) {
                $memberName = trim($member['first_name'] . ' ' . $member['last_name']);
                
                // Prepare template data
                $templateData = [
                    'member_name' => $memberName,
                    'member_id' => $member['member_id'],
                    'plan_name' => $member['plan_name'] ?? 'Standard Plan',
                    'expired_date' => date('F j, Y', strtotime($member['end_date']))
                ];
                
                // Send expired membership email
                if (!empty($member['email']) && $this->emailService->isConfigured()) {
                    try {
                        $emailResult = $this->emailService->sendTemplatedEmail(
                            $member['email'],
                            $memberName,
                            'membership_expired',
                            $templateData
                        );
                        
                        if ($emailResult['success']) {
                            $emailCount++;
                            error_log("AutoNotifications: Expired membership email sent to $memberName");
                        } else {
                            error_log("AutoNotifications: Expired membership email failed for $memberName: " . $emailResult['message']);
                        }
                    } catch (Exception $e) {
                        error_log("AutoNotifications: Expired membership email exception for $memberName: " . $e->getMessage());
                    }
                }
                
                // Send expired membership SMS
                if (!empty($member['phone']) && $this->smsService->isConfigured()) {
                    try {
                        $smsResult = $this->smsService->sendTemplatedSms(
                            $member['phone'],
                            'membership_expired',
                            $templateData
                        );
                        
                        if ($smsResult['success']) {
                            $smsCount++;
                            error_log("AutoNotifications: Expired membership SMS sent to $memberName");
                        } else {
                            error_log("AutoNotifications: Expired membership SMS failed for $memberName: " . $smsResult['message']);
                        }
                    } catch (Exception $e) {
                        error_log("AutoNotifications: Expired membership SMS exception for $memberName: " . $e->getMessage());
                    }
                }
                
                // Update member status to expired
                $db->update('members', ['status' => 'expired'], 'id = ?', [$member['id']]);
            }
            
            return [
                'success' => true,
                'message' => "Expired membership notifications sent: $emailCount emails, $smsCount SMS",
                'email_count' => $emailCount,
                'sms_count' => $smsCount,
                'total_members' => count($expiredMembers)
            ];
            
        } catch (Exception $e) {
            error_log("AutoNotifications: Expired membership notifications failed: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Check if notifications are configured
     */
    public function isConfigured() {
        return $this->emailService->isConfigured() || $this->smsService->isConfigured();
    }
}
?>
