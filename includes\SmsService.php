<?php
/**
 * SMS Service Class
 * MyGym Management System
 * 
 * Handles SMS sending functionality with multiple provider support
 */

require_once 'Config.php';
require_once 'SmsTemplates.php';

class SmsService {
    private $provider;
    private $api_key;
    private $sender_id;
    private $api_url;

    public function __construct() {
        // Load SMS settings from config
        $this->provider = Config::get('sms_provider') ?: 'twilio';
        $this->api_key = Config::get('sms_api_key');
        $this->sender_id = Config::get('sms_sender_id') ?: Config::get('gym_name') ?: 'MyGym';
        
        // Set API URL based on provider
        $this->setApiUrl();
    }

    /**
     * Set API URL based on SMS provider
     */
    private function setApiUrl() {
        switch ($this->provider) {
            case 'smsgate':
                // SMS-Gate.app uses your custom server URL
                $this->api_url = Config::get('sms_server_url') ?: 'http://your-android-device-ip:8080';
                break;
            case 'twilio':
                $this->api_url = 'https://api.twilio.com/2010-04-01/Accounts/';
                break;
            case 'textlocal':
                $this->api_url = 'https://api.textlocal.in/send/';
                break;
            case 'nexmo':
                $this->api_url = 'https://rest.nexmo.com/sms/json';
                break;
            case 'msg91':
                $this->api_url = 'https://api.msg91.com/api/sendhttp.php';
                break;
            default:
                $this->api_url = '';
        }
    }

    /**
     * Check if SMS is properly configured
     */
    public function isConfigured() {
        if ($this->provider === 'smsgate') {
            // For SMS-Gate, we need server URL, API key is optional
            return !empty(Config::get('sms_server_url'));
        }
        return !empty($this->api_key) && !empty($this->provider);
    }

    /**
     * Send SMS message
     */
    public function sendSms($to_phone, $message) {
        if (!$this->isConfigured()) {
            return [
                'success' => false,
                'message' => 'SMS not configured. Please configure SMS settings in Settings page.'
            ];
        }

        // Validate and format phone number based on provider
        if ($this->provider === 'smsgate') {
            $phone = $this->formatPhoneForSmsGate($to_phone);
        } else {
            $phone = SmsTemplates::validatePhoneNumber($to_phone);
        }

        if (!$phone) {
            return [
                'success' => false,
                'message' => 'Invalid phone number format.'
            ];
        }

        // Check message length
        $charCount = SmsTemplates::getCharacterCount($message);
        $smsCount = SmsTemplates::getSmsCount($message);

        try {
            switch ($this->provider) {
                case 'smsgate':
                    return $this->sendViaSmsGate($phone, $message);
                case 'twilio':
                    return $this->sendViaTwilio($phone, $message);
                case 'textlocal':
                    return $this->sendViaTextLocal($phone, $message);
                case 'nexmo':
                    return $this->sendViaNexmo($phone, $message);
                case 'msg91':
                    return $this->sendViaMsg91($phone, $message);
                default:
                    return $this->sendViaDemo($phone, $message);
            }
        } catch (Exception $e) {
            error_log('SMS Error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to send SMS: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send SMS via SMS-Gate.app (Android device or cloud)
     */
    private function sendViaSmsGate($phone, $message) {
        $serverUrl = Config::get('sms_server_url');
        $username = Config::get('sms_username') ?: '';
        $password = $this->api_key ?: '';

        if (empty($serverUrl)) {
            return [
                'success' => false,
                'message' => 'SMS-Gate server URL not configured'
            ];
        }

        // Detect if using cloud server or local server
        $isCloudServer = strpos($serverUrl, 'api.sms-gate.app') !== false;

        if ($isCloudServer) {
            // Cloud server endpoint
            $endpoint = 'https://api.sms-gate.app/3rdparty/v1/message';
        } else {
            // Local server endpoint
            $endpoint = rtrim($serverUrl, '/') . '/message';
        }

        // Prepare the data according to SMS-Gate API format
        $postData = [
            'textMessage' => [
                'text' => $message
            ],
            'phoneNumbers' => [$phone]
        ];

        // Log the request for debugging
        error_log("SMS-Gate Request: " . json_encode([
            'mode' => $isCloudServer ? 'cloud' : 'local',
            'endpoint' => $endpoint,
            'username' => $username,
            'password_set' => !empty($password),
            'original_phone' => $phone,
            'formatted_phone' => $postData['phoneNumbers'][0],
            'data' => $postData
        ]));

        // Initialize cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'User-Agent: MyGym-SMS-Client/1.0'
        ]);

        // Add SSL configuration for cloud server
        if ($isCloudServer) {
            // Try to use proper SSL verification first
            $caBundlePath = $this->findCaBundlePath();

            if ($caBundlePath && file_exists($caBundlePath)) {
                // Use proper SSL verification with CA bundle
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
                curl_setopt($ch, CURLOPT_CAINFO, $caBundlePath);
                error_log("SMS-Gate: Using CA bundle: " . $caBundlePath);
            } else {
                // Fallback: Disable SSL verification (not recommended for production)
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                error_log("SMS-Gate: SSL verification disabled - CA bundle not found");
            }
        }

        // Add basic authentication - SMS-Gate requires this
        if (!empty($username) && !empty($password)) {
            curl_setopt($ch, CURLOPT_USERPWD, $username . ':' . $password);
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        }

        // Execute the request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $info = curl_getinfo($ch);
        curl_close($ch);

        // Log the response for debugging
        error_log("SMS-Gate Response: HTTP {$httpCode}, Error: {$error}, Response: " . substr($response, 0, 200));

        // Handle cURL errors
        if ($error) {
            $errorMsg = 'Connection error: ' . $error . '. ';
            if ($isCloudServer) {
                $errorMsg .= 'Make sure your internet connection is working and SMS-Gate cloud service is available.';
            } else {
                $errorMsg .= 'Make sure SMS-Gate app is running on your Android device and the server URL is correct.';
            }
            return [
                'success' => false,
                'message' => $errorMsg
            ];
        }

        // Handle HTTP errors
        if ($httpCode === 401) {
            return [
                'success' => false,
                'message' => 'Authentication failed. Check your SMS-Gate username and password in Settings.'
            ];
        } elseif ($httpCode === 404) {
            $errorMsg = 'SMS-Gate endpoint not found. ';
            if ($isCloudServer) {
                $errorMsg .= 'Make sure your device is connected to SMS-Gate cloud service.';
            } else {
                $errorMsg .= 'Make sure the server URL is correct and the app is running.';
            }
            return [
                'success' => false,
                'message' => $errorMsg
            ];
        } elseif (!in_array($httpCode, [200, 201, 202])) {
            return [
                'success' => false,
                'message' => "HTTP Error {$httpCode}: Unable to connect to SMS-Gate server. Response: " . substr($response, 0, 100)
            ];
        }

        // Parse response - SMS-Gate typically returns JSON
        $responseData = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            // If not JSON, check if it's a successful plain text response
            if (in_array($httpCode, [200, 201, 202])) {
                return [
                    'success' => true,
                    'message' => 'SMS sent successfully via SMS-Gate ' . ($isCloudServer ? 'Cloud' : 'Local'),
                    'provider' => 'SMS-Gate',
                    'phone' => SmsTemplates::formatPhoneNumber($phone),
                    'character_count' => SmsTemplates::getCharacterCount($message),
                    'sms_count' => SmsTemplates::getSmsCount($message),
                    'raw_response' => $response
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Invalid response from SMS-Gate: ' . substr($response, 0, 100)
                ];
            }
        }

        // Check response format - SMS-Gate may return different formats
        if (isset($responseData['success']) && $responseData['success']) {
            return [
                'success' => true,
                'message' => 'SMS sent successfully via SMS-Gate ' . ($isCloudServer ? 'Cloud' : 'Local'),
                'provider' => 'SMS-Gate',
                'phone' => SmsTemplates::formatPhoneNumber($phone),
                'character_count' => SmsTemplates::getCharacterCount($message),
                'sms_count' => SmsTemplates::getSmsCount($message),
                'response' => $responseData
            ];
        } elseif (isset($responseData['id'])) {
            // SMS-Gate returns message ID on success (both local and cloud)
            $state = $responseData['state'] ?? 'Unknown';
            $statusMessage = 'SMS queued successfully via SMS-Gate ' . ($isCloudServer ? 'Cloud' : 'Local');

            if ($state === 'Pending') {
                $statusMessage .= ' - Message is pending delivery to your Android device';
            } elseif ($state === 'Sent') {
                $statusMessage .= ' - Message sent successfully';
            } elseif ($state === 'Delivered') {
                $statusMessage .= ' - Message delivered successfully';
            } else {
                $statusMessage .= " - Status: {$state}";
            }

            return [
                'success' => true,
                'message' => $statusMessage,
                'provider' => 'SMS-Gate',
                'phone' => SmsTemplates::formatPhoneNumber($phone),
                'character_count' => SmsTemplates::getCharacterCount($message),
                'sms_count' => SmsTemplates::getSmsCount($message),
                'message_id' => $responseData['id'],
                'device_id' => $responseData['deviceId'] ?? null,
                'state' => $state,
                'response' => $responseData
            ];
        } else {
            $errorMessage = $responseData['message'] ?? $responseData['error'] ?? 'Unknown error from SMS-Gate server';
            return [
                'success' => false,
                'message' => 'SMS-Gate error: ' . $errorMessage
            ];
        }
    }

    /**
     * Send SMS via Twilio
     */
    private function sendViaTwilio($phone, $message) {
        // For demo purposes - in real implementation, use Twilio SDK
        return $this->sendViaDemo($phone, $message, 'Twilio');
    }

    /**
     * Send SMS via TextLocal
     */
    private function sendViaTextLocal($phone, $message) {
        // For demo purposes - in real implementation, use TextLocal API
        return $this->sendViaDemo($phone, $message, 'TextLocal');
    }

    /**
     * Send SMS via Nexmo/Vonage
     */
    private function sendViaNexmo($phone, $message) {
        // For demo purposes - in real implementation, use Nexmo SDK
        return $this->sendViaDemo($phone, $message, 'Nexmo');
    }

    /**
     * Send SMS via MSG91
     */
    private function sendViaMsg91($phone, $message) {
        // For demo purposes - in real implementation, use MSG91 API
        return $this->sendViaDemo($phone, $message, 'MSG91');
    }

    /**
     * Demo SMS sender (for testing without actual SMS provider)
     */
    private function sendViaDemo($phone, $message, $provider = 'Demo') {
        // Log the SMS for demo purposes
        $logMessage = sprintf(
            "[%s] SMS via %s to %s: %s",
            date('Y-m-d H:i:s'),
            $provider,
            SmsTemplates::formatPhoneNumber($phone),
            substr($message, 0, 100) . (strlen($message) > 100 ? '...' : '')
        );
        
        error_log($logMessage);

        return [
            'success' => true,
            'message' => "SMS sent successfully via {$provider} (Demo Mode)",
            'provider' => $provider,
            'phone' => SmsTemplates::formatPhoneNumber($phone),
            'character_count' => SmsTemplates::getCharacterCount($message),
            'sms_count' => SmsTemplates::getSmsCount($message)
        ];
    }

    /**
     * Send templated SMS
     */
    public function sendTemplatedSms($to_phone, $templateType, $templateData = []) {
        if (!$this->isConfigured()) {
            return [
                'success' => false,
                'message' => 'SMS not configured. Please configure SMS settings in Settings page.'
            ];
        }

        try {
            // Generate template content
            $message = SmsTemplates::generateTemplate($templateType, $templateData);

            // Send SMS
            return $this->sendSms($to_phone, $message);

        } catch (Exception $e) {
            error_log('Templated SMS Error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to send templated SMS: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send bulk SMS to multiple recipients
     */
    public function sendBulkSms($recipients, $message) {
        $results = [];
        $successCount = 0;
        $failCount = 0;

        foreach ($recipients as $recipient) {
            $phone = $recipient['phone'] ?? '';
            $name = $recipient['name'] ?? 'Member';

            if (empty($phone)) {
                $results[] = [
                    'name' => $name,
                    'phone' => $phone,
                    'success' => false,
                    'message' => 'No phone number provided'
                ];
                $failCount++;
                continue;
            }

            // Personalize message if needed
            $personalizedMessage = str_replace('{member_name}', $name, $message);
            $personalizedMessage = str_replace('{gym_name}', $this->sender_id, $personalizedMessage);

            $result = $this->sendSms($phone, $personalizedMessage);
            
            $results[] = [
                'name' => $name,
                'phone' => SmsTemplates::formatPhoneNumber($phone),
                'success' => $result['success'],
                'message' => $result['message']
            ];

            if ($result['success']) {
                $successCount++;
            } else {
                $failCount++;
            }

            // Add small delay to avoid rate limiting
            usleep(100000); // 0.1 second delay
        }

        return [
            'success' => $successCount > 0,
            'message' => "Bulk SMS completed: {$successCount} sent, {$failCount} failed",
            'total_sent' => $successCount,
            'total_failed' => $failCount,
            'results' => $results
        ];
    }

    /**
     * Test SMS configuration
     */
    public function testConfiguration($testPhone = null) {
        if (!$this->isConfigured()) {
            return [
                'success' => false,
                'message' => 'SMS not configured. Please set up SMS provider and API key.'
            ];
        }

        $testMessage = "Test message from {$this->sender_id}. SMS configuration is working correctly!";
        
        if ($testPhone) {
            return $this->sendSms($testPhone, $testMessage);
        } else {
            return [
                'success' => true,
                'message' => 'SMS configuration appears valid. Provider: ' . ucfirst($this->provider),
                'provider' => $this->provider,
                'sender_id' => $this->sender_id
            ];
        }
    }

    /**
     * Get SMS provider options
     */
    /**
     * Format phone number specifically for SMS-Gate (requires international format)
     */
    private function formatPhoneForSmsGate($phone) {
        // Remove all non-numeric characters except +
        $phone = preg_replace('/[^0-9+]/', '', $phone);

        // If phone already starts with +, validate and return
        if (strpos($phone, '+') === 0) {
            $numericPart = substr($phone, 1);
            if (strlen($numericPart) >= 10 && strlen($numericPart) <= 15) {
                return $phone;
            }
            return false;
        }

        // Remove any + that's not at the beginning
        $phone = str_replace('+', '', $phone);

        // Add country code if missing
        if (strlen($phone) == 10) {
            // Assume US/Canada number, add +1
            $phone = '+1' . $phone;
        } elseif (strlen($phone) == 11 && substr($phone, 0, 1) == '1') {
            // US/Canada number with 1 prefix, add +
            $phone = '+' . $phone;
        } elseif (strlen($phone) >= 10 && strlen($phone) <= 15) {
            // International number without country code, assume it needs +
            // For now, we'll assume it's complete and add +
            $phone = '+' . $phone;
        } else {
            // Invalid length
            return false;
        }

        // Final validation
        $numericPart = substr($phone, 1);
        if (strlen($numericPart) >= 10 && strlen($numericPart) <= 15) {
            return $phone;
        }

        return false;
    }

    /**
     * Find CA bundle path for SSL verification
     */
    private function findCaBundlePath() {
        // Common locations for CA bundle files
        $possiblePaths = [
            // From PHP configuration
            ini_get('curl.cainfo'),
            ini_get('openssl.cafile'),

            // Common Windows locations
            'C:\wamp64\bin\php\php' . PHP_MAJOR_VERSION . '.' . PHP_MINOR_VERSION . '.' . PHP_RELEASE_VERSION . '\extras\ssl\cacert.pem',
            'C:\xampp\php\extras\ssl\cacert.pem',
            'C:\php\extras\ssl\cacert.pem',

            // Common Linux locations
            '/etc/ssl/certs/ca-certificates.crt',
            '/etc/ssl/certs/ca-bundle.crt',
            '/usr/share/ssl/certs/ca-bundle.crt',
            '/usr/local/share/certs/ca-root-nss.crt',

            // Try downloading from curl website (fallback)
            __DIR__ . '/../cacert.pem'
        ];

        foreach ($possiblePaths as $path) {
            if ($path && file_exists($path)) {
                return $path;
            }
        }

        // Try to download CA bundle if none found
        $downloadPath = __DIR__ . '/../cacert.pem';
        if (!file_exists($downloadPath)) {
            $this->downloadCaBundleIfNeeded($downloadPath);
        }

        return file_exists($downloadPath) ? $downloadPath : null;
    }

    /**
     * Download CA bundle from curl website if needed
     */
    private function downloadCaBundleIfNeeded($downloadPath) {
        try {
            $caBundleUrl = 'https://curl.se/ca/cacert.pem';
            $context = stream_context_create([
                'http' => [
                    'timeout' => 30,
                    'user_agent' => 'MyGym-SMS-Client/1.0'
                ],
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false
                ]
            ]);

            $caBundleContent = file_get_contents($caBundleUrl, false, $context);
            if ($caBundleContent !== false) {
                file_put_contents($downloadPath, $caBundleContent);
                error_log("SMS-Gate: Downloaded CA bundle to " . $downloadPath);
                return true;
            }
        } catch (Exception $e) {
            error_log("SMS-Gate: Failed to download CA bundle: " . $e->getMessage());
        }

        return false;
    }

    public static function getProviderOptions() {
        return [
            'smsgate' => 'SMS-Gate.app (Android Device)',
            'twilio' => 'Twilio',
            'textlocal' => 'TextLocal',
            'nexmo' => 'Nexmo/Vonage',
            'msg91' => 'MSG91',
            'demo' => 'Demo Mode (Testing)'
        ];
    }

    /**
     * Get SMS statistics
     */
    public function getStatistics() {
        // In a real implementation, this would fetch from database
        return [
            'total_sent' => 0,
            'sent_today' => 0,
            'sent_this_month' => 0,
            'failed_count' => 0,
            'provider' => $this->provider,
            'configured' => $this->isConfigured()
        ];
    }
}
?>
