<?php
/**
 * MyGym Management System - Dashboard
 * Main admin dashboard with widgets and analytics
 */

session_start();
require_once 'config/database.php';
require_once 'includes/auth.php';

// Require authentication
Auth::requireAuth();

// Get dashboard statistics
try {
    $db = Database::getInstance();
    
    // Total members
    $totalMembers = $db->fetch("SELECT COUNT(*) as count FROM members")['count'];
    
    // Active members
    $activeMembers = $db->fetch("SELECT COUNT(*) as count FROM members WHERE status = 'active' AND end_date >= CURDATE()")['count'];
    
    // Expiring soon (next 7 days)
    $expiringSoon = $db->fetch("SELECT COUNT(*) as count FROM members WHERE status = 'active' AND end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)")['count'];
    
    // Today's payments
    $todayPayments = $db->fetch("SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE payment_date = CURDATE()")['total'];
    
    // Monthly revenue (current month)
    $monthlyRevenue = $db->fetch("SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE YEAR(payment_date) = YEAR(CURDATE()) AND MONTH(payment_date) = MONTH(CURDATE())")['total'];
    
    // Recent members (last 5)
    $recentMembers = $db->fetchAll("
        SELECT m.*, p.name as plan_name 
        FROM members m 
        LEFT JOIN plans p ON m.plan_id = p.id 
        ORDER BY m.created_at DESC 
        LIMIT 5
    ");
    
    // Expiring members alert
    $expiringMembers = $db->fetchAll("
        SELECT m.*, p.name as plan_name 
        FROM members m 
        LEFT JOIN plans p ON m.plan_id = p.id 
        WHERE m.status = 'active' AND m.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
        ORDER BY m.end_date ASC
    ");
    
    // Monthly revenue chart data (last 6 months)
    $chartData = $db->fetchAll("
        SELECT 
            DATE_FORMAT(payment_date, '%Y-%m') as month,
            SUM(amount) as revenue
        FROM payments 
        WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(payment_date, '%Y-%m')
        ORDER BY month ASC
    ");
    
} catch (Exception $e) {
    $error = 'Failed to load dashboard data: ' . $e->getMessage();
}

$pageTitle = 'Dashboard';
include 'includes/header.php';
?>

<div class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">Welcome back, <?= htmlspecialchars(Auth::user()['name']) ?>!</h1>
                <p class="opacity-90">Here's what's happening at your gym today.</p>
            </div>
            <div class="text-right">
                <p class="text-sm opacity-75">Today</p>
                <p class="text-xl font-semibold"><?= date('M d, Y') ?></p>
            </div>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Members -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Members</p>
                    <p class="text-3xl font-bold text-gray-900"><?= number_format($totalMembers ?? 0) ?></p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-users text-blue-600 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-green-600 font-medium">+12%</span>
                <span class="text-gray-500 ml-1">from last month</span>
            </div>
        </div>
        
        <!-- Active Members -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Active Members</p>
                    <p class="text-3xl font-bold text-gray-900"><?= number_format($activeMembers ?? 0) ?></p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-user-check text-green-600 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-green-600 font-medium">+8%</span>
                <span class="text-gray-500 ml-1">from last month</span>
            </div>
        </div>
        
        <!-- Expiring Soon -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Expiring Soon</p>
                    <p class="text-3xl font-bold text-gray-900"><?= number_format($expiringSoon ?? 0) ?></p>
                </div>
                <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-gray-500">Next 7 days</span>
            </div>
        </div>
        
        <!-- Today's Revenue -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Today's Revenue</p>
                    <p class="text-3xl font-bold text-gray-900"><?= formatCurrency($todayPayments) ?></p>
                </div>
                <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-dollar-sign text-purple-600 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-green-600 font-medium">+15%</span>
                <span class="text-gray-500 ml-1">from yesterday</span>
            </div>
        </div>
    </div>
    
    <!-- Alerts Section -->
    <?php if (!empty($expiringMembers)): ?>
    <div class="bg-yellow-50 border border-yellow-200 rounded-2xl p-6">
        <div class="flex items-center mb-4">
            <i class="fas fa-bell text-yellow-600 text-xl mr-3"></i>
            <h3 class="text-lg font-semibold text-yellow-900">Membership Expiring Soon</h3>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <?php foreach (array_slice($expiringMembers, 0, 6) as $member): ?>
            <div class="bg-white rounded-lg p-4 border border-yellow-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="font-medium text-gray-900"><?= htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) ?></p>
                        <p class="text-sm text-gray-600"><?= htmlspecialchars($member['plan_name']) ?></p>
                        <p class="text-sm text-yellow-600">Expires: <?= formatDate($member['end_date']) ?></p>
                    </div>
                    <a href="pages/renewals.php?member_id=<?= $member['id'] ?>"
                       class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded-lg text-sm transition duration-200">
                        <i class="fas fa-sync-alt mr-1"></i>Renew
                    </a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <div class="mt-4 text-center">
            <a href="pages/renewals.php" class="inline-flex items-center text-yellow-600 hover:text-yellow-800 font-medium">
                <i class="fas fa-sync-alt mr-2"></i>
                <?php if (count($expiringMembers) > 6): ?>
                    View all <?= count($expiringMembers) ?> expiring memberships →
                <?php else: ?>
                    Manage Renewals →
                <?php endif; ?>
            </a>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Revenue Chart -->
        <div class="lg:col-span-2 bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">Monthly Revenue</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-500">This month:</span>
                    <span class="text-lg font-bold text-green-600"><?= formatCurrency($monthlyRevenue) ?></span>
                </div>
            </div>
            <div class="h-64">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>
        
        <!-- Recent Members -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">Recent Members</h3>
                <a href="pages/members.php" class="text-blue-600 hover:text-blue-800 text-sm font-medium">View all</a>
            </div>
            <div class="space-y-4">
                <?php foreach ($recentMembers as $member): ?>
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <?php if ($member['avatar']): ?>
                            <img src="<?= htmlspecialchars($member['avatar']) ?>" alt="" class="w-10 h-10 rounded-full object-cover">
                        <?php else: ?>
                            <i class="fas fa-user text-gray-500"></i>
                        <?php endif; ?>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">
                            <?= htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) ?>
                        </p>
                        <p class="text-sm text-gray-500"><?= htmlspecialchars($member['plan_name'] ?? 'No plan') ?></p>
                    </div>
                    <div class="text-right">
                        <p class="text-xs text-gray-500"><?= timeAgo($member['created_at']) ?></p>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="pages/members.php?action=add" 
               class="flex flex-col items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-xl transition duration-200">
                <i class="fas fa-user-plus text-blue-600 text-2xl mb-2"></i>
                <span class="text-sm font-medium text-blue-900">Add Member</span>
            </a>
            
            <a href="pages/renewals.php"
               class="flex flex-col items-center p-4 bg-green-50 hover:bg-green-100 rounded-xl transition duration-200">
                <i class="fas fa-sync-alt text-green-600 text-2xl mb-2"></i>
                <span class="text-sm font-medium text-green-900">Manage Renewals</span>
            </a>
            
            <a href="pages/trainers.php?action=add" 
               class="flex flex-col items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-xl transition duration-200">
                <i class="fas fa-user-tie text-purple-600 text-2xl mb-2"></i>
                <span class="text-sm font-medium text-purple-900">Add Trainer</span>
            </a>
            
            <a href="pages/reports.php" 
               class="flex flex-col items-center p-4 bg-orange-50 hover:bg-orange-100 rounded-xl transition duration-200">
                <i class="fas fa-chart-bar text-orange-600 text-2xl mb-2"></i>
                <span class="text-sm font-medium text-orange-900">View Reports</span>
            </a>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const ctx = document.getElementById('revenueChart').getContext('2d');
const chartData = <?= json_encode($chartData) ?>;

const labels = chartData.map(item => {
    const date = new Date(item.month + '-01');
    return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
});

const data = chartData.map(item => parseFloat(item.revenue));

new Chart(ctx, {
    type: 'line',
    data: {
        labels: labels,
        datasets: [{
            label: 'Revenue',
            data: data,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            }
        }
    }
});
</script>

<?php include 'includes/footer.php'; ?>
