<?php
/**
 * Payments Management Page
 * MyGym Management System
 */

session_start();
require_once '../config/database.php';
require_once '../includes/auth.php';

// Require authentication and permission
Auth::requireAuth();
Auth::requirePermission('payments.view');

$db = Database::getInstance();
$action = $_GET['action'] ?? 'list';
$paymentId = $_GET['id'] ?? null;
$memberId = $_GET['member_id'] ?? null;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add') {
        $result = handlePaymentAdd();
        if ($result['success']) {
            $receiptMessage = $result['message'] . ' <a href="receipt.php?id=' . $result['payment_id'] . '" class="text-blue-600 hover:text-blue-800 underline ml-2">View Receipt</a>';
            Session::success($receiptMessage);
            header('Location: payments.php');
            exit;
        } else {
            $error = $result['message'];
        }
    } elseif ($action === 'delete') {
        $result = handlePaymentDelete();
        if ($result['success']) {
            Session::success($result['message']);
        } else {
            Session::error($result['message']);
        }
        header('Location: payments.php');
        exit;
    }
}

function handlePaymentAdd() {
    global $db;
    
    $memberId = $_POST['member_id'] ?? null;
    $planId = $_POST['plan_id'] ?? null;
    $amount = $_POST['amount'] ?? null;
    $paymentMethod = $_POST['payment_method'] ?? 'cash';
    $transactionId = trim($_POST['transaction_id'] ?? '');
    $paymentDate = $_POST['payment_date'] ?? date('Y-m-d');
    $notes = trim($_POST['notes'] ?? '');
    
    if (!$memberId || !$planId || !$amount) {
        return ['success' => false, 'message' => 'Member, plan, and amount are required.'];
    }
    
    if (!is_numeric($amount) || $amount <= 0) {
        return ['success' => false, 'message' => 'Please enter a valid amount.'];
    }
    
    try {
        $db->beginTransaction();
        
        // Get member and plan details
        $member = $db->fetch("SELECT * FROM members WHERE id = ?", [$memberId]);
        $plan = $db->fetch("SELECT * FROM plans WHERE id = ?", [$planId]);
        
        if (!$member || !$plan) {
            throw new Exception('Member or plan not found.');
        }
        
        // Generate receipt number
        $receiptNumber = 'RCP' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        // Calculate membership dates
        $startDate = $member['end_date'] && $member['end_date'] >= date('Y-m-d') 
            ? $member['end_date'] 
            : $paymentDate;
        
        $endDate = date('Y-m-d', strtotime($startDate . ' + ' . $plan['duration_months'] . ' months'));
        
        // Handle processed_by field for local admin vs database users
        $processedBy = null;
        if (Auth::isLocalAdmin()) {
            // For local admins, set processed_by to null since they don't exist in users table
            $processedBy = null;
        } else {
            // For database users, use their actual user ID
            $processedBy = Auth::id();
        }

        // Insert payment record
        $paymentData = [
            'member_id' => $memberId,
            'plan_id' => $planId,
            'amount' => $amount,
            'payment_method' => $paymentMethod,
            'transaction_id' => $transactionId ?: null,
            'receipt_number' => $receiptNumber,
            'payment_date' => $paymentDate,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'notes' => ($notes ?: null) . (Auth::isLocalAdmin() ? ' (Local Admin)' : ''),
            'processed_by' => $processedBy
        ];
        
        $paymentId = $db->insert('payments', $paymentData);
        
        // Update member's plan and dates
        $memberUpdateData = [
            'plan_id' => $planId,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'status' => 'active'
        ];
        
        $db->update('members', $memberUpdateData, 'id = ?', [$memberId]);
        
        // Log activity
        ActivityLogger::log('Payment Added', 'payments', $paymentId, null, $paymentData);

        // Send payment confirmation notifications (email and SMS)
        try {
            require_once '../includes/AutoNotifications.php';
            $autoNotifications = new AutoNotifications();
            $confirmationResults = $autoNotifications->sendPaymentConfirmationNotifications($paymentId);

            if ($confirmationResults) {
                error_log("Payment confirmation notifications sent for payment ID: $paymentId");
            }
        } catch (Exception $e) {
            // Don't fail the payment if notifications fail, just log it
            error_log('Payment confirmation notifications failed: ' . $e->getMessage());
        }

        $db->commit();
        return ['success' => true, 'message' => 'Payment recorded successfully. Receipt: ' . $receiptNumber, 'payment_id' => $paymentId, 'receipt_number' => $receiptNumber];
        
    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'Failed to record payment: ' . $e->getMessage()];
    }
}

function handlePaymentDelete() {
    global $db;
    
    $paymentId = $_POST['payment_id'] ?? null;
    if (!$paymentId) {
        return ['success' => false, 'message' => 'Payment ID is required.'];
    }
    
    try {
        $payment = $db->fetch("SELECT * FROM payments WHERE id = ?", [$paymentId]);
        if (!$payment) {
            return ['success' => false, 'message' => 'Payment not found.'];
        }
        
        $db->beginTransaction();
        
        // Delete payment
        $db->delete('payments', 'id = ?', [$paymentId]);
        
        // Log activity
        ActivityLogger::log('Payment Deleted', 'payments', $paymentId, $payment);
        
        $db->commit();
        return ['success' => true, 'message' => 'Payment deleted successfully.'];
        
    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'Failed to delete payment: ' . $e->getMessage()];
    }
}

// Get data for forms
$members = $db->fetchAll("
    SELECT id, CONCAT(first_name, ' ', last_name) as name, member_id 
    FROM members 
    WHERE status = 'active' 
    ORDER BY first_name, last_name
");

$plans = $db->fetchAll("SELECT id, name, price, duration_months FROM plans WHERE is_active = 1 ORDER BY name");

// Get member data if specified
$selectedMember = null;
if ($memberId) {
    $selectedMember = $db->fetch("
        SELECT m.*, p.name as plan_name 
        FROM members m 
        LEFT JOIN plans p ON m.plan_id = p.id 
        WHERE m.id = ?
    ", [$memberId]);
}

// Get payments list with pagination and search
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;
$search = trim($_GET['search'] ?? '');
$methodFilter = $_GET['method'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';

$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(m.first_name LIKE ? OR m.last_name LIKE ? OR m.member_id LIKE ? OR p.receipt_number LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
}

if ($methodFilter) {
    $whereConditions[] = "p.payment_method = ?";
    $params[] = $methodFilter;
}

if ($dateFrom) {
    $whereConditions[] = "p.payment_date >= ?";
    $params[] = $dateFrom;
}

if ($dateTo) {
    $whereConditions[] = "p.payment_date <= ?";
    $params[] = $dateTo;
}

$whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get total count
$totalPayments = $db->fetch("
    SELECT COUNT(*) as count 
    FROM payments p 
    JOIN members m ON p.member_id = m.id 
    $whereClause
", $params)['count'];

$totalPages = ceil($totalPayments / $limit);

// Get payments
$payments = $db->fetchAll("
    SELECT p.*, 
           CONCAT(m.first_name, ' ', m.last_name) as member_name,
           m.member_id,
           pl.name as plan_name,
           u.name as processed_by_name
    FROM payments p
    JOIN members m ON p.member_id = m.id
    LEFT JOIN plans pl ON p.plan_id = pl.id
    LEFT JOIN users u ON p.processed_by = u.id
    $whereClause
    ORDER BY p.payment_date DESC, p.created_at DESC
    LIMIT $limit OFFSET $offset
", $params);

// Calculate totals
$totalAmount = $db->fetch("
    SELECT COALESCE(SUM(p.amount), 0) as total 
    FROM payments p 
    JOIN members m ON p.member_id = m.id 
    $whereClause
", $params)['total'];

$pageTitle = 'Payments';
include '../includes/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Payments</h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Manage membership payments and renewals</p>
        </div>
        
        <?php if (Auth::can('payments.create')): ?>
        <div class="mt-4 sm:mt-0">
            <a href="payments.php?action=add<?= $memberId ? '&member_id=' . $memberId : '' ?>" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                <i class="fas fa-plus mr-2"></i>
                Record Payment
            </a>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Payments</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($totalPayments ?? 0) ?></p>
                </div>
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-receipt text-blue-600 dark:text-blue-400"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Amount</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= formatCurrency($totalAmount) ?></p>
                </div>
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-dollar-sign text-green-600 dark:text-green-400"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Today's Revenue</p>
                    <?php
                    $todayRevenue = $db->fetch("SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE payment_date = CURDATE()")['total'];
                    ?>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= formatCurrency($todayRevenue) ?></p>
                </div>
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-calendar-day text-purple-600 dark:text-purple-400"></i>
                </div>
            </div>
        </div>
    </div>

    <?php if ($action === 'list'): ?>
    <!-- Filters and Search -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                <input type="text"
                       name="search"
                       value="<?= htmlspecialchars($search) ?>"
                       placeholder="Search payments..."
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Payment Method</label>
                <select name="method" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="">All Methods</option>
                    <option value="cash" <?= $methodFilter === 'cash' ? 'selected' : '' ?>>Cash</option>
                    <option value="card" <?= $methodFilter === 'card' ? 'selected' : '' ?>>Card</option>
                    <option value="bank_transfer" <?= $methodFilter === 'bank_transfer' ? 'selected' : '' ?>>Bank Transfer</option>
                    <option value="online" <?= $methodFilter === 'online' ? 'selected' : '' ?>>Online</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">From Date</label>
                <input type="date"
                       name="date_from"
                       value="<?= htmlspecialchars($dateFrom) ?>"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">To Date</label>
                <input type="date"
                       name="date_to"
                       value="<?= htmlspecialchars($dateTo) ?>"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
            </div>

            <div class="flex items-end space-x-2">
                <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-search mr-2"></i>Filter
                </button>
                <a href="payments.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </form>
    </div>

    <!-- Payments Table -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    Payment History (<?= number_format($totalPayments) ?>)
                </h3>
                <div class="flex items-center space-x-2">
                    <button onclick="exportData('payments', 'csv')"
                            class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Receipt</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Member</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Plan</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Method</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Processed By</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    <?php foreach ($payments as $payment): ?>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                <?= htmlspecialchars($payment['receipt_number']) ?>
                            </div>
                            <?php if ($payment['transaction_id']): ?>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    TXN: <?= htmlspecialchars($payment['transaction_id']) ?>
                                </div>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                <?= htmlspecialchars($payment['member_name']) ?>
                            </div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                ID: <?= htmlspecialchars($payment['member_id']) ?>
                            </div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                            <?= htmlspecialchars($payment['plan_name']) ?>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                <?= formatCurrency($payment['amount']) ?>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <?php
                            $methodColors = [
                                'cash' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                                'card' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
                                'bank_transfer' => 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
                                'online' => 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
                            ];
                            $methodColor = $methodColors[$payment['payment_method']] ?? $methodColors['cash'];
                            ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?= $methodColor ?>">
                                <?= ucfirst(str_replace('_', ' ', $payment['payment_method'])) ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                            <?= formatDate($payment['payment_date']) ?>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                            <?php
                            if ($payment['processed_by_name']) {
                                echo htmlspecialchars($payment['processed_by_name']);
                            } elseif (strpos($payment['notes'], '(Local Admin)') !== false) {
                                echo '<span class="text-blue-600 dark:text-blue-400">Local Admin</span>';
                            } else {
                                echo 'System';
                            }
                            ?>
                        </td>
                        <td class="px-6 py-4 text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <button onclick="viewReceipt(<?= $payment['id'] ?>)"
                                        class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                                        title="View Receipt">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button onclick="showPrintOptions(<?= $payment['id'] ?>)"
                                        class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                        title="Print Receipt">
                                    <i class="fas fa-print"></i>
                                </button>
                                <a href="payments.php?member_id=<?= $payment['member_id'] ?>"
                                   class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                                   title="Member Payments">
                                    <i class="fas fa-user"></i>
                                </a>
                                <?php if (Auth::can('payments.delete')): ?>
                                <button onclick="deletePayment(<?= $payment['id'] ?>, '<?= htmlspecialchars($payment['receipt_number']) ?>')"
                                        class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                        title="Delete Payment">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>

                    <?php if (empty($payments)): ?>
                    <tr>
                        <td colspan="8" class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                            <i class="fas fa-receipt text-4xl mb-4"></i>
                            <p class="text-lg font-medium">No payments found</p>
                            <p class="mt-1">Start by recording your first payment.</p>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($action === 'add'): ?>
    <!-- Add Payment Form -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Record New Payment</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Process a membership payment and renewal</p>
        </div>

        <?php if ($error): ?>
            <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <i class="fas fa-exclamation-circle text-red-400 mt-0.5 mr-3"></i>
                    <span class="text-red-800"><?= htmlspecialchars($error) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <form method="POST" class="space-y-6" x-data="paymentForm">
            <?= Auth::csrfField() ?>

            <!-- Member Selection -->
            <div>
                <label for="member_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Select Member <span class="text-red-500">*</span>
                </label>
                <select id="member_id"
                        name="member_id"
                        x-model="selectedMember"
                        @change="updateMemberInfo()"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        required>
                    <option value="">Choose a member...</option>
                    <?php foreach ($members as $memberOption): ?>
                        <option value="<?= $memberOption['id'] ?>" <?= $memberId == $memberOption['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($memberOption['name']) ?> (<?= htmlspecialchars($memberOption['member_id']) ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- Member Info Display -->
            <?php if ($selectedMember): ?>
            <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">Current Member Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span class="text-blue-700 dark:text-blue-300">Current Plan:</span>
                        <p class="font-medium text-blue-900 dark:text-blue-100">
                            <?= htmlspecialchars($selectedMember['plan_name'] ?? 'No active plan') ?>
                        </p>
                    </div>
                    <div>
                        <span class="text-blue-700 dark:text-blue-300">Membership Expires:</span>
                        <p class="font-medium text-blue-900 dark:text-blue-100">
                            <?= $selectedMember['end_date'] ? formatDate($selectedMember['end_date']) : 'N/A' ?>
                        </p>
                    </div>
                    <div>
                        <span class="text-blue-700 dark:text-blue-300">Status:</span>
                        <p class="font-medium text-blue-900 dark:text-blue-100">
                            <?= ucfirst($selectedMember['status']) ?>
                        </p>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Plan and Amount -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="plan_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Membership Plan <span class="text-red-500">*</span>
                    </label>
                    <select id="plan_id"
                            name="plan_id"
                            x-model="selectedPlan"
                            @change="updateAmount()"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                            required>
                        <option value="">Select plan...</option>
                        <?php foreach ($plans as $plan): ?>
                            <option value="<?= $plan['id'] ?>" data-price="<?= $plan['price'] ?>" data-duration="<?= $plan['duration_months'] ?>">
                                <?= htmlspecialchars($plan['name']) ?> - <?= formatCurrency($plan['price']) ?> (<?= $plan['duration_months'] ?> months)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div>
                    <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Amount <span class="text-red-500">*</span>
                    </label>
                    <input type="number"
                           id="amount"
                           name="amount"
                           x-model="amount"
                           step="0.01"
                           min="0"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                           required>
                </div>
            </div>

            <!-- Payment Details -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="payment_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Payment Method <span class="text-red-500">*</span>
                    </label>
                    <select id="payment_method"
                            name="payment_method"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                            required>
                        <option value="cash">Cash</option>
                        <option value="card">Card</option>
                        <option value="bank_transfer">Bank Transfer</option>
                        <option value="online">Online</option>
                    </select>
                </div>

                <div>
                    <label for="transaction_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Transaction ID
                    </label>
                    <input type="text"
                           id="transaction_id"
                           name="transaction_id"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                           placeholder="Optional">
                </div>

                <div>
                    <label for="payment_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Payment Date <span class="text-red-500">*</span>
                    </label>
                    <input type="date"
                           id="payment_date"
                           name="payment_date"
                           value="<?= date('Y-m-d') ?>"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                           required>
                </div>
            </div>

            <!-- Notes -->
            <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</label>
                <textarea id="notes"
                          name="notes"
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                          placeholder="Any additional notes about this payment..."></textarea>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <a href="payments.php"
                   class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition duration-200">
                    Cancel
                </a>
                <button type="submit"
                        class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                    <i class="fas fa-credit-card mr-2"></i>
                    Record Payment
                </button>
            </div>
        </form>
    </div>

    <script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('paymentForm', () => ({
            selectedMember: '<?= $memberId ?? '' ?>',
            selectedPlan: '',
            amount: '',

            updateAmount() {
                const planSelect = document.getElementById('plan_id');
                const selectedOption = planSelect.options[planSelect.selectedIndex];
                if (selectedOption && selectedOption.dataset.price) {
                    this.amount = selectedOption.dataset.price;
                }
            },

            updateMemberInfo() {
                // This would typically fetch member info via AJAX
                // For now, we'll reload the page with the selected member
                if (this.selectedMember) {
                    window.location.href = `payments.php?action=add&member_id=${this.selectedMember}`;
                }
            }
        }));
    });
    </script>
    <?php endif; ?>
</div>

<script>
function deletePayment(paymentId, receiptNumber) {
    showConfirmModal({
        title: 'Delete Payment',
        message: `Are you sure you want to delete payment <strong>${receiptNumber}</strong>?<br><br>This action cannot be undone and will affect financial records.`,
        confirmText: 'Delete Payment',
        cancelText: 'Cancel',
        type: 'danger',
        onConfirm: () => {
            // Create and submit form
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            form.innerHTML = `
                <input type="hidden" name="payment_id" value="${paymentId}">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="csrf_token" value="<?= Auth::generateCsrfToken() ?>">
            `;

            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>

<?php include '../includes/footer.php'; ?>
