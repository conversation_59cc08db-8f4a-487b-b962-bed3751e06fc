<?php
/**
 * Debug Login Issue
 * Check what's happening with the login credentials
 */

// Check if config exists
if (!file_exists('config/config.php')) {
    die('System not installed. Please run install.php first.');
}

require_once 'config/database.php';

echo "<h1>MyGym Login Debug</h1>\n";

try {
    $db = Database::getInstance();
    
    // Check if users table exists and has data
    echo "<h3>1. Checking Users Table</h3>\n";
    
    $users = $db->fetchAll("SELECT id, name, email, role, is_active, created_at FROM users");
    
    if (empty($users)) {
        echo "❌ No users found in database<br>\n";
        echo "This means the installation didn't create the admin user properly.<br>\n";
    } else {
        echo "✅ Found " . count($users) . " user(s):<br>\n";
        foreach ($users as $user) {
            echo "- ID: {$user['id']}, Name: {$user['name']}, Email: {$user['email']}, Role: {$user['role']}, Active: {$user['is_active']}<br>\n";
        }
    }
    
    // Test login form
    echo "<h3>2. Test Login</h3>\n";
    
    if ($_POST['test_login'] ?? false) {
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        
        echo "<h4>Testing login for: {$email}</h4>\n";
        
        // Get user from database
        $user = $db->fetch(
            "SELECT id, name, email, password, role, is_active FROM users WHERE email = ?",
            [$email]
        );
        
        if (!$user) {
            echo "❌ User not found with email: {$email}<br>\n";
        } else {
            echo "✅ User found:<br>\n";
            echo "- Name: {$user['name']}<br>\n";
            echo "- Email: {$user['email']}<br>\n";
            echo "- Role: {$user['role']}<br>\n";
            echo "- Active: {$user['is_active']}<br>\n";
            echo "- Password hash: " . substr($user['password'], 0, 20) . "...<br>\n";
            
            if ($user['is_active'] != 1) {
                echo "❌ User account is not active<br>\n";
            } else {
                // Test password verification
                if (password_verify($password, $user['password'])) {
                    echo "✅ Password verification successful!<br>\n";
                    echo "<strong>Login should work. Check for other issues like session or redirects.</strong><br>\n";
                } else {
                    echo "❌ Password verification failed<br>\n";
                    echo "The password you entered doesn't match the stored hash.<br>\n";
                    
                    // Test if password was stored as plain text (installation bug)
                    if ($password === $user['password']) {
                        echo "⚠️ Password is stored as plain text! This is a security issue.<br>\n";
                        echo "The installation process didn't hash the password properly.<br>\n";
                    }
                }
            }
        }
    }
    
    echo '<form method="post" style="margin: 20px 0; padding: 20px; border: 1px solid #ccc; background: #f9f9f9;">
        <h4>Test Your Login Credentials</h4>
        <p>Email: <input type="email" name="email" required style="width: 200px; padding: 5px;"></p>
        <p>Password: <input type="password" name="password" required style="width: 200px; padding: 5px;"></p>
        <p><button type="submit" name="test_login" value="1" style="padding: 10px 20px; background: #007cba; color: white; border: none;">Test Login</button></p>
    </form>';
    
    // Check for common issues
    echo "<h3>3. Common Issues Check</h3>\n";
    
    // Check session configuration
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    echo "✅ Session started successfully<br>\n";
    echo "Session ID: " . session_id() . "<br>\n";
    
    // Check if config file is readable
    if (is_readable('config/config.php')) {
        echo "✅ Config file is readable<br>\n";
    } else {
        echo "❌ Config file is not readable<br>\n";
    }
    
    // Check database connection
    echo "✅ Database connection working<br>\n";
    
    echo "<h3>4. Possible Solutions</h3>\n";
    echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #007cba;'>\n";
    echo "<h4>If login still doesn't work:</h4>\n";
    echo "<ol>\n";
    echo "<li><strong>Clear browser cache and cookies</strong></li>\n";
    echo "<li><strong>Try incognito/private browsing mode</strong></li>\n";
    echo "<li><strong>Check if JavaScript is enabled</strong></li>\n";
    echo "<li><strong>Verify the email address is exactly as entered during installation</strong></li>\n";
    echo "<li><strong>If password verification fails, the installation may have had an issue</strong></li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>\n";
}

echo "<hr>\n";
echo "<p><a href='index.php'>← Back to Login</a></p>\n";
