<?php
/**
 * Database Restore Page
 * For new installations or complete system restore
 */

session_start();
require_once 'config/database.php';
require_once 'includes/SimpleBackup.php';

// Check if this is a fresh installation (no admin user)
$db = Database::getInstance();
$hasAdmin = false;

try {
    $adminCheck = $db->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
    $hasAdmin = $adminCheck && $adminCheck['count'] > 0;
} catch (Exception $e) {
    // Database might not be set up yet
    $hasAdmin = false;
}

// Handle restore request
$restoreResult = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $backup = new SimpleBackup();
    
    if ($_POST['action'] === 'upload_and_restore' && isset($_FILES['backup_file'])) {
        // Upload and restore
        $uploadResult = $backup->uploadBackup($_FILES['backup_file']);
        if ($uploadResult['success']) {
            $restoreResult = $backup->restoreBackup($uploadResult['filename']);
            if ($restoreResult['success']) {
                // Redirect to login after successful restore
                header('Location: pages/login.php?restored=1');
                exit;
            }
        } else {
            $restoreResult = $uploadResult;
        }
    } elseif ($_POST['action'] === 'restore' && isset($_POST['filename'])) {
        // Restore existing backup
        $restoreResult = $backup->restoreBackup($_POST['filename']);
        if ($restoreResult['success']) {
            header('Location: pages/login.php?restored=1');
            exit;
        }
    }
}

// Get available backups
$backup = new SimpleBackup();
$backups = $backup->getBackupList();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Restore - MyGym</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    <i class="fas fa-database text-blue-600 mr-3"></i>Database Restore
                </h1>
                <p class="text-gray-600">
                    <?php if (!$hasAdmin): ?>
                        Restore your MyGym database from a backup file
                    <?php else: ?>
                        <span class="text-orange-600 font-semibold">⚠️ Warning: This will replace all current data</span>
                    <?php endif; ?>
                </p>
            </div>

            <!-- Status Messages -->
            <?php if ($restoreResult): ?>
                <div class="mb-6 p-4 rounded-lg <?= $restoreResult['success'] ? 'bg-green-100 border border-green-200 text-green-800' : 'bg-red-100 border border-red-200 text-red-800' ?>">
                    <div class="flex items-center">
                        <i class="fas fa-<?= $restoreResult['success'] ? 'check-circle' : 'exclamation-circle' ?> mr-2"></i>
                        <?= htmlspecialchars($restoreResult['message']) ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Upload and Restore -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">
                    <i class="fas fa-upload text-purple-600 mr-2"></i>Upload & Restore Backup
                </h2>
                
                <form method="POST" enctype="multipart/form-data" class="space-y-4">
                    <input type="hidden" name="action" value="upload_and_restore">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Select Backup File (.sql)
                        </label>
                        <input type="file" 
                               name="backup_file" 
                               accept=".sql"
                               required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <p class="text-sm text-gray-500 mt-1">
                            Maximum file size: 50MB. Only .sql files are accepted.
                        </p>
                    </div>
                    
                    <?php if ($hasAdmin): ?>
                        <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                            <div class="flex items-start">
                                <i class="fas fa-exclamation-triangle text-red-600 mr-3 mt-1"></i>
                                <div class="text-sm text-red-800">
                                    <strong>Warning:</strong> This will completely replace all current data including members, payments, and settings. This action cannot be undone.
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <button type="submit" 
                            class="w-full px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors font-medium">
                        <i class="fas fa-upload mr-2"></i>Upload & Restore Database
                    </button>
                </form>
            </div>

            <!-- Existing Backups -->
            <?php if (!empty($backups)): ?>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">
                        <i class="fas fa-list text-green-600 mr-2"></i>Available Backups
                    </h2>
                    
                    <div class="space-y-3">
                        <?php foreach ($backups as $backup_item): ?>
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                <div class="flex-1">
                                    <div class="font-medium text-gray-900"><?= htmlspecialchars($backup_item['filename']) ?></div>
                                    <div class="text-sm text-gray-500"><?= $backup_item['date'] ?> • <?= $backup_item['size'] ?></div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <a href="api/simple-backup.php?action=download&filename=<?= urlencode($backup_item['filename']) ?>"
                                       class="px-3 py-1 text-sm bg-blue-100 hover:bg-blue-200 text-blue-700 rounded transition-colors"
                                       title="Download">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <form method="POST" class="inline" onsubmit="return confirmRestore('<?= htmlspecialchars($backup_item['filename']) ?>')">
                                        <input type="hidden" name="action" value="restore">
                                        <input type="hidden" name="filename" value="<?= htmlspecialchars($backup_item['filename']) ?>">
                                        <button type="submit" 
                                                class="px-3 py-1 text-sm bg-green-100 hover:bg-green-200 text-green-700 rounded transition-colors"
                                                title="Restore">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Navigation -->
            <div class="mt-8 text-center">
                <?php if ($hasAdmin): ?>
                    <a href="pages/dashboard.php" class="text-blue-600 hover:text-blue-800 mr-4">
                        <i class="fas fa-arrow-left mr-1"></i>Back to Dashboard
                    </a>
                    <a href="pages/settings.php" class="text-blue-600 hover:text-blue-800">
                        <i class="fas fa-cog mr-1"></i>Settings
                    </a>
                <?php else: ?>
                    <a href="pages/login.php" class="text-blue-600 hover:text-blue-800">
                        <i class="fas fa-sign-in-alt mr-1"></i>Go to Login
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function confirmRestore(filename) {
            return confirm(`⚠️ WARNING: This will restore the database from "${filename}" and REPLACE ALL CURRENT DATA.\n\nThis action cannot be undone. Are you sure you want to proceed?`);
        }
    </script>
</body>
</html>
