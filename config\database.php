<?php
/**
 * Database Configuration and Connection Handler
 * MyGym Management System
 */

class Database {
    private static $instance = null;
    private $connection;
    private $host;
    private $database;
    private $username;
    private $password;
    
    private function __construct() {
        // Load database configuration
        $this->loadConfig();
        $this->connect();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function loadConfig() {
        $configFile = __DIR__ . '/config.php';
        if (file_exists($configFile)) {
            $config = require $configFile;
            $this->host = $config['database']['host'];
            $this->database = $config['database']['name'];
            $this->username = $config['database']['username'];
            $this->password = $config['database']['password'];
        } else {
            throw new Exception('Database configuration file not found. Please run the installation wizard.');
        }
    }
    
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->database};charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
        } catch (PDOException $e) {
            throw new Exception('Database connection failed: ' . $e->getMessage());
        }
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            throw new Exception('Database query failed: ' . $e->getMessage());
        }
    }
    
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        return $this->connection->lastInsertId();
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $column) {
            $setClause[] = "{$column} = :{$column}";
        }
        $setClause = implode(', ', $setClause);

        // Convert positional parameters to named parameters to avoid mixing
        $namedWhereParams = [];
        if (!empty($whereParams)) {
            // If whereParams are positional (numeric keys), convert to named
            if (array_keys($whereParams) === range(0, count($whereParams) - 1)) {
                // Convert WHERE clause from ? to named parameters
                $whereParamCount = substr_count($where, '?');
                for ($i = 0; $i < $whereParamCount; $i++) {
                    $paramName = 'where_param_' . $i;
                    $namedWhereParams[$paramName] = $whereParams[$i] ?? null;
                    $where = preg_replace('/\?/', ':' . $paramName, $where, 1);
                }
            } else {
                // Already named parameters
                $namedWhereParams = $whereParams;
            }
        }

        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $namedWhereParams);

        return $this->query($sql, $params);
    }
    
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($sql, $params);
    }
    
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    public function commit() {
        return $this->connection->commit();
    }
    
    public function rollback() {
        // Check if there's an active transaction before rolling back
        if ($this->connection->inTransaction()) {
            return $this->connection->rollback();
        }
        return false;
    }
    
    public function getLastInsertId() {
        return $this->connection->lastInsertId();
    }
    
    // Prevent cloning
    private function __clone() {}
    
    // Prevent unserialization
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * Helper function to get database instance
 */
function db() {
    return Database::getInstance();
}

// Config class is now loaded from includes/Config.php to avoid conflicts

// ActivityLogger class is now loaded from includes/ActivityLogger.php to avoid conflicts
