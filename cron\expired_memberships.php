<?php
/**
 * Expired Membership Notifications Cron Job
 * MyGym Management System
 * 
 * This script should be run daily to check for expired memberships
 * and send notifications to members.
 * 
 * Usage: php expired_memberships.php
 * Or set up as a cron job: 0 9 * * * /usr/bin/php /path/to/mygym/cron/expired_memberships.php
 */

// Set the working directory to the project root
chdir(dirname(__DIR__));

// Include required files
require_once 'config/database.php';
require_once 'includes/AutoNotifications.php';
require_once 'includes/Config.php';

// Prevent running from web browser for security
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

// Log start of script
error_log("Expired Membership Notifications Cron Job Started: " . date('Y-m-d H:i:s'));

try {
    // Initialize database connection
    $db = Database::getInstance();
    
    // Initialize auto notifications
    $autoNotifications = new AutoNotifications();
    
    // Check if notifications are configured
    if (!$autoNotifications->isConfigured()) {
        error_log("Expired Membership Cron: No email or SMS service configured. Skipping notifications.");
        exit(0);
    }
    
    // Send expired membership notifications
    $result = $autoNotifications->sendExpiredMembershipNotifications();
    
    if ($result['success']) {
        $message = "Expired Membership Notifications: " . $result['message'];
        error_log($message);
        echo $message . "\n";
        
        // Log detailed results
        if (isset($result['email_count']) && isset($result['sms_count'])) {
            $details = "Details: {$result['email_count']} emails sent, {$result['sms_count']} SMS sent to {$result['total_members']} expired members";
            error_log($details);
            echo $details . "\n";
        }
    } else {
        $errorMessage = "Expired Membership Notifications Failed: " . $result['message'];
        error_log($errorMessage);
        echo $errorMessage . "\n";
        exit(1);
    }
    
} catch (Exception $e) {
    $errorMessage = "Expired Membership Cron Job Failed: " . $e->getMessage();
    error_log($errorMessage);
    echo $errorMessage . "\n";
    exit(1);
}

// Log completion
error_log("Expired Membership Notifications Cron Job Completed: " . date('Y-m-d H:i:s'));
echo "Expired Membership Notifications Cron Job Completed Successfully\n";
exit(0);
?>
