# MyGym Automated Notifications - <PERSON>ron Jobs

This directory contains scripts for automated notifications that should be run on a schedule.

## Scripts

### `expired_memberships.php`
Sends notifications to members whose memberships have expired.

**What it does:**
- Finds members whose memberships expired yesterday
- Sends email and SMS notifications to expired members
- Updates member status to 'expired'
- Logs all activities

**Frequency:** Should run daily (recommended: 9 AM)

## Setup Instructions

### Windows (WAMP/XAMPP)

1. **Using Windows Task Scheduler:**
   ```
   Program: C:\wamp64\bin\php\php8.2.0\php.exe
   Arguments: C:\wamp64\www\mygym\cron\expired_memberships.php
   Start in: C:\wamp64\www\mygym
   ```

2. **Schedule:** Daily at 9:00 AM

### Linux/Unix

1. **Edit crontab:**
   ```bash
   crontab -e
   ```

2. **Add this line:**
   ```
   0 9 * * * /usr/bin/php /path/to/mygym/cron/expired_memberships.php
   ```

### Manual Testing

You can test the scripts manually:

```bash
# Navigate to MyGym directory
cd /path/to/mygym

# Run the script
php cron/expired_memberships.php
```

## Requirements

- PHP CLI access
- MyGym database configured
- Email and/or SMS services configured in MyGym settings

## Logs

All cron job activities are logged to your PHP error log. Check your server's error log for:
- `Expired Membership Notifications Cron Job Started`
- `Expired Membership Notifications: X emails sent, Y SMS sent`
- `Expired Membership Notifications Cron Job Completed`

## Manual Trigger

You can also manually trigger expired membership notifications from the MyGym admin panel:
1. Go to **Notifications** page
2. Find **"Automated Notifications"** section
3. Click **"Send Now"** under **"Expired Memberships"**

## Troubleshooting

### Script not running
- Check PHP CLI path: `which php` or `where php`
- Verify file permissions: `chmod +x expired_memberships.php`
- Check cron service is running: `service cron status`

### No notifications sent
- Verify email/SMS services are configured in MyGym settings
- Check if there are actually expired members
- Review PHP error logs for detailed error messages

### Permission issues
- Ensure web server user can read/write to MyGym directory
- Check database connection permissions
