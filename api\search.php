<?php
/**
 * Global Search API
 * MyGym Management System
 */

session_start();
require_once '../config/database.php';
require_once '../includes/auth.php';

// Require authentication
if (!Auth::check()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Set JSON header
header('Content-Type: application/json');

try {
    $query = trim($_GET['q'] ?? '');
    
    if (strlen($query) < 2) {
        echo json_encode(['success' => true, 'results' => []]);
        exit;
    }
    
    $db = Database::getInstance();
    $searchTerm = "%$query%";
    $results = [];
    
    // Search Members
    if (Auth::can('members.view')) {
        $members = $db->fetchAll("
            SELECT 
                'member' as type,
                id,
                CONCAT(first_name, ' ', last_name) as title,
                member_id as subtitle,
                email as description,
                status,
                CONCAT('pages/members.php?action=view&id=', id) as url
            FROM members 
            WHERE (first_name LIKE ? OR last_name LIKE ? OR email LIKE ? OR phone LIKE ? OR member_id LIKE ?)
            ORDER BY first_name, last_name
            LIMIT 5
        ", [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        
        $results = array_merge($results, $members);
    }
    
    // Search Payments/Receipts
    if (Auth::can('payments.view')) {
        $payments = $db->fetchAll("
            SELECT 
                'payment' as type,
                p.id,
                CONCAT('Receipt #', p.receipt_number) as title,
                CONCAT(m.first_name, ' ', m.last_name) as subtitle,
                CONCAT('$', FORMAT(p.amount, 2), ' - ', p.payment_method) as description,
                p.payment_date as date,
                CONCAT('pages/receipt.php?id=', p.id) as url
            FROM payments p
            LEFT JOIN members m ON p.member_id = m.id
            WHERE (p.receipt_number LIKE ? OR m.first_name LIKE ? OR m.last_name LIKE ?)
            ORDER BY p.payment_date DESC
            LIMIT 5
        ", [$searchTerm, $searchTerm, $searchTerm]);
        
        $results = array_merge($results, $payments);
    }
    
    // Search Trainers
    if (Auth::can('trainers.view')) {
        $trainers = $db->fetchAll("
            SELECT 
                'trainer' as type,
                id,
                name as title,
                specialty as subtitle,
                email as description,
                is_active as status,
                CONCAT('pages/trainers.php?action=view&id=', id) as url
            FROM trainers 
            WHERE (name LIKE ? OR email LIKE ? OR specialty LIKE ?)
            ORDER BY name
            LIMIT 5
        ", [$searchTerm, $searchTerm, $searchTerm]);
        
        $results = array_merge($results, $trainers);
    }
    
    // Search Plans
    if (Auth::can('plans.view')) {
        $plans = $db->fetchAll("
            SELECT 
                'plan' as type,
                id,
                name as title,
                CONCAT('$', FORMAT(price, 2), ' / ', duration_months, ' months') as subtitle,
                description,
                is_active as status,
                CONCAT('pages/plans.php?action=view&id=', id) as url
            FROM plans 
            WHERE (name LIKE ? OR description LIKE ?)
            ORDER BY name
            LIMIT 5
        ", [$searchTerm, $searchTerm]);
        
        $results = array_merge($results, $plans);
    }
    
    // Search Equipment
    if (Auth::can('equipment.view')) {
        $equipment = $db->fetchAll("
            SELECT 
                'equipment' as type,
                id,
                name as title,
                CONCAT(brand, ' ', model) as subtitle,
                CONCAT(type, ' - ', condition_status) as description,
                is_active as status,
                CONCAT('pages/equipment.php?action=view&id=', id) as url
            FROM equipment 
            WHERE (name LIKE ? OR brand LIKE ? OR model LIKE ? OR serial_number LIKE ?)
            ORDER BY name
            LIMIT 5
        ", [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        
        $results = array_merge($results, $equipment);
    }
    
    // Limit total results
    $results = array_slice($results, 0, 15);
    
    echo json_encode([
        'success' => true,
        'results' => $results,
        'query' => $query,
        'total' => count($results)
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Search failed: ' . $e->getMessage()
    ]);
}
?>
