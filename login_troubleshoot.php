<?php
/**
 * MyGym Login Troubleshoot
 * Customer support tool for login issues
 * Safe for production - includes security checks
 */

// Security: Only allow on localhost or with admin override
$isLocalhost = in_array($_SERVER['HTTP_HOST'] ?? '', ['localhost', '127.0.0.1', '::1']);
$hasOverride = isset($_GET['admin_override']) && $_GET['admin_override'] === 'mygym2025';

if (!$isLocalhost && !$hasOverride) {
    die('Access denied. This troubleshooting tool is only available on localhost or with admin override.');
}

// Check if system is installed
if (!file_exists('config/config.php')) {
    die('System not installed. Please run install.php first.');
}

require_once 'config/database.php';

echo "<h1>MyGym Login Troubleshoot</h1>\n";

// Test login credentials
if ($_POST['test_login'] ?? false) {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if ($email && $password) {
        try {
            $db = Database::getInstance();
            
            echo "<h3>🔍 Testing Login Credentials</h3>\n";
            echo "<p><strong>Email:</strong> {$email}</p>\n";
            
            // Check if user exists
            $user = $db->fetch("SELECT id, name, email, password, role, is_active FROM users WHERE LOWER(email) = LOWER(?)", [$email]);
            
            if (!$user) {
                echo "<p style='color: #dc3545;'>❌ No user found with email: {$email}</p>\n";
                
                // Show available users (admin only)
                $users = $db->fetchAll("SELECT name, email, role FROM users WHERE is_active = 1 ORDER BY role, name");
                if (!empty($users)) {
                    echo "<p><strong>Available users:</strong></p>\n";
                    echo "<ul>\n";
                    foreach ($users as $u) {
                        echo "<li>{$u['name']} ({$u['email']}) - {$u['role']}</li>\n";
                    }
                    echo "</ul>\n";
                }
            } else {
                echo "<p style='color: #28a745;'>✅ User found: {$user['name']} ({$user['role']})</p>\n";
                
                if ($user['is_active'] != 1) {
                    echo "<p style='color: #dc3545;'>❌ User account is inactive</p>\n";
                } else {
                    echo "<p style='color: #28a745;'>✅ User account is active</p>\n";
                    
                    // Test password
                    if (password_verify($password, $user['password'])) {
                        echo "<p style='color: #28a745;'>✅ Password verification successful!</p>\n";
                        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
                        echo "<h4>✅ Login Should Work!</h4>\n";
                        echo "<p>Your credentials are correct. If login still fails, try:</p>\n";
                        echo "<ul>\n";
                        echo "<li>Clear browser cache and cookies</li>\n";
                        echo "<li>Try incognito/private browsing mode</li>\n";
                        echo "<li>Disable browser extensions temporarily</li>\n";
                        echo "<li>Check if JavaScript is enabled</li>\n";
                        echo "</ul>\n";
                        echo "<p><a href='index.php' style='color: #155724; font-weight: bold;'>→ Try Login Again</a></p>\n";
                        echo "</div>\n";
                    } else {
                        echo "<p style='color: #dc3545;'>❌ Password verification failed</p>\n";
                        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
                        echo "<h4>❌ Password Issue Detected</h4>\n";
                        echo "<p>The password you entered doesn't match the stored password.</p>\n";
                        echo "<p><strong>Solutions:</strong></p>\n";
                        echo "<ul>\n";
                        echo "<li>Double-check your password (case-sensitive)</li>\n";
                        echo "<li>Try the password you used during installation</li>\n";
                        echo "<li>Contact system administrator for password reset</li>\n";
                        echo "</ul>\n";
                        echo "</div>\n";
                    }
                }
            }
        } catch (Exception $e) {
            echo "<p style='color: #dc3545;'>❌ Database error: " . $e->getMessage() . "</p>\n";
        }
    }
}

// Show system status
try {
    $db = Database::getInstance();
    
    echo "<h3>📊 System Status</h3>\n";
    
    // Count users by role
    $userStats = $db->fetchAll("SELECT role, COUNT(*) as count FROM users WHERE is_active = 1 GROUP BY role");
    
    echo "<table style='border-collapse: collapse; margin: 20px 0;'>\n";
    echo "<tr style='background: #f8f9fa;'>\n";
    echo "<th style='border: 1px solid #dee2e6; padding: 8px; text-align: left;'>Role</th>\n";
    echo "<th style='border: 1px solid #dee2e6; padding: 8px; text-align: left;'>Active Users</th>\n";
    echo "</tr>\n";
    
    foreach ($userStats as $stat) {
        echo "<tr>\n";
        echo "<td style='border: 1px solid #dee2e6; padding: 8px;'>" . ucfirst($stat['role']) . "</td>\n";
        echo "<td style='border: 1px solid #dee2e6; padding: 8px;'>{$stat['count']}</td>\n";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Check database connection
    echo "<p style='color: #28a745;'>✅ Database connection working</p>\n";
    
    // Check essential settings
    $gymName = $db->fetch("SELECT value FROM settings WHERE `key` = 'gym_name'");
    if ($gymName) {
        echo "<p style='color: #28a745;'>✅ System configured (Gym: {$gymName['value']})</p>\n";
    } else {
        echo "<p style='color: #ffc107;'>⚠️ System settings may be incomplete</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: #dc3545;'>❌ System error: " . $e->getMessage() . "</p>\n";
}

// Test form
echo "<h3>🧪 Test Your Login</h3>\n";
echo '<form method="post" style="margin: 20px 0; padding: 20px; border: 1px solid #ccc; background: #f9f9f9; border-radius: 5px;">
    <div style="margin-bottom: 15px;">
        <label for="email" style="display: block; margin-bottom: 5px; font-weight: bold;">Email:</label>
        <input type="email" id="email" name="email" required style="width: 100%; max-width: 300px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;" placeholder="<EMAIL>" value="' . htmlspecialchars($_POST['email'] ?? '') . '">
    </div>
    
    <div style="margin-bottom: 20px;">
        <label for="password" style="display: block; margin-bottom: 5px; font-weight: bold;">Password:</label>
        <input type="password" id="password" name="password" required style="width: 100%; max-width: 300px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;" placeholder="Your password">
    </div>
    
    <button type="submit" name="test_login" value="1" style="padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">Test Login</button>
</form>';

echo "<h3>📞 Support Information</h3>\n";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
echo "<h4>Common Login Issues & Solutions:</h4>\n";
echo "<ol>\n";
echo "<li><strong>Wrong Email:</strong> Use the exact email from installation</li>\n";
echo "<li><strong>Wrong Password:</strong> Passwords are case-sensitive</li>\n";
echo "<li><strong>Browser Issues:</strong> Clear cache, try incognito mode</li>\n";
echo "<li><strong>JavaScript Disabled:</strong> Enable JavaScript in browser</li>\n";
echo "<li><strong>Cookies Disabled:</strong> Enable cookies for this site</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<hr>\n";
echo "<p><a href='index.php'>← Back to Login</a></p>\n";

// Show access info for non-localhost
if (!$isLocalhost) {
    echo "<p style='font-size: 12px; color: #666;'>Accessed with admin override from: " . ($_SERVER['HTTP_HOST'] ?? 'unknown') . "</p>\n";
}
