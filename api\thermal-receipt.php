<?php
/**
 * Thermal Receipt Printer - ESC/POS Compatible
 * MyGym Management System
 */

session_start();

// Check authentication
if (!isset($_SESSION['user_id']) && !isset($_SESSION['local_admin_username'])) {
    http_response_code(401);
    exit('Unauthorized');
}

// Get payment ID
$paymentId = $_GET['id'] ?? null;
if (!$paymentId || !is_numeric($paymentId)) {
    http_response_code(400);
    exit('Invalid payment ID');
}

try {
    // Include required files
    require_once '../config/database.php';
    require_once '../includes/auth.php';

    $db = Database::getInstance();

    // Get payment details (NO sensitive information for security)
    $payment = $db->fetch("
        SELECT p.*,
               CONCAT(m.first_name, ' ', m.last_name) as member_name,
               m.member_id,
               pl.name as plan_name, pl.duration_months,
               u.name as processed_by_name
        FROM payments p
        LEFT JOIN members m ON p.member_id = m.id
        LEFT JOIN plans pl ON p.plan_id = pl.id
        LEFT JOIN users u ON p.processed_by = u.id
        WHERE p.id = ?
    ", [$paymentId]);

    if (!$payment) {
        http_response_code(404);
        exit('Payment not found');
    }

    // Get gym settings with fallbacks
    try {
        $gymName = Config::get('gym_name', 'MyGym');
        $gymAddress = Config::get('gym_address', '');
        $gymPhone = Config::get('gym_phone', '');
        $gymEmail = Config::get('gym_email', '');
        $currency = Config::get('currency', 'USD');
    } catch (Exception $configError) {
        // Fallback to defaults if Config/settings table doesn't exist
        $gymName = 'MyGym';
        $gymAddress = '';
        $gymPhone = '';
        $gymEmail = '';
        $currency = 'USD';
    }

    // formatCurrency function is already available from auth.php

    // Generate thermal receipt content
    $receiptWidth = 32; // Characters per line for thermal printer

    function centerText($text, $width = 32) {
        $len = strlen($text);
        if ($len >= $width) return $text;
        $padding = ($width - $len) / 2;
        return str_repeat(' ', floor($padding)) . $text . str_repeat(' ', ceil($padding));
    }

    function leftRightText($left, $right, $width = 32) {
        $leftLen = strlen($left);
        $rightLen = strlen($right);
        $spaces = $width - $leftLen - $rightLen;
        if ($spaces < 1) $spaces = 1;
        return $left . str_repeat(' ', $spaces) . $right;
    }

    function createBox($text, $width = 32) {
        $lines = [];
        $lines[] = '+' . str_repeat('-', $width - 2) . '+';
        $lines[] = '|' . centerText($text, $width - 2) . '|';
        $lines[] = '+' . str_repeat('-', $width - 2) . '+';
        return implode("\n", $lines);
    }

    function wrapText($text, $width = 32) {
        return wordwrap($text, $width, "\n", true);
    }

    // Set headers for plain text output
    header('Content-Type: text/plain; charset=utf-8');
    header('Content-Disposition: inline; filename="receipt_' . $payment['receipt_number'] . '.txt"');

    // Generate cool thermal receipt content
    echo "\n";

    // ASCII Art Header
    echo centerText("╔══════════════════════════════╗") . "\n";
    echo centerText("║          💪 GYM 💪           ║") . "\n";
    echo centerText("╚══════════════════════════════╝") . "\n";
    echo "\n";

    // Gym Name with style
    echo centerText("★ " . strtoupper($gymName) . " ★") . "\n";
    if ($gymAddress) {
        echo centerText(wrapText($gymAddress, 30)) . "\n";
    }
    if ($gymPhone) echo centerText("📞 " . $gymPhone) . "\n";
    if ($gymEmail) echo centerText("📧 " . $gymEmail) . "\n";

    echo "\n";
    echo str_repeat('═', $receiptWidth) . "\n";
    echo centerText("🧾 PAYMENT RECEIPT 🧾") . "\n";
    echo str_repeat('═', $receiptWidth) . "\n";

    // Receipt number in a box
    echo createBox($payment['receipt_number']) . "\n";

    // Member info section
    echo "\n";
    echo "┌" . str_repeat('─', $receiptWidth - 2) . "┐\n";
    echo "│" . centerText("👤 MEMBER INFO", $receiptWidth - 2) . "│\n";
    echo "├" . str_repeat('─', $receiptWidth - 2) . "┤\n";
    echo "│ Name: " . str_pad($payment['member_name'], $receiptWidth - 9) . "│\n";
    echo "│ ID:   " . str_pad($payment['member_id'], $receiptWidth - 9) . "│\n";
    echo "└" . str_repeat('─', $receiptWidth - 2) . "┘\n";

    echo "\n";

    // Payment details section
    echo "┌" . str_repeat('─', $receiptWidth - 2) . "┐\n";
    echo "│" . centerText("💳 PAYMENT DETAILS", $receiptWidth - 2) . "│\n";
    echo "├" . str_repeat('─', $receiptWidth - 2) . "┤\n";
    echo "│ Plan: " . str_pad(substr($payment['plan_name'] ?? 'N/A', 0, $receiptWidth - 9), $receiptWidth - 9) . "│\n";
    echo "│ Duration: " . str_pad(($payment['duration_months'] ?? 0) . " month(s)", $receiptWidth - 13) . "│\n";
    echo "│ Method: " . str_pad(ucfirst($payment['payment_method']), $receiptWidth - 11) . "│\n";
    echo "│ Date: " . str_pad(date('M j, Y', strtotime($payment['payment_date'])), $receiptWidth - 9) . "│\n";
    echo "└" . str_repeat('─', $receiptWidth - 2) . "┘\n";

    echo "\n";

    // Membership period
    echo "┌" . str_repeat('─', $receiptWidth - 2) . "┐\n";
    echo "│" . centerText("📅 MEMBERSHIP PERIOD", $receiptWidth - 2) . "│\n";
    echo "├" . str_repeat('─', $receiptWidth - 2) . "┤\n";
    echo "│ Start: " . str_pad(date('M j, Y', strtotime($payment['start_date'])), $receiptWidth - 10) . "│\n";
    echo "│ End:   " . str_pad(date('M j, Y', strtotime($payment['end_date'])), $receiptWidth - 10) . "│\n";
    echo "└" . str_repeat('─', $receiptWidth - 2) . "┘\n";

    echo "\n";

    // Total amount - highlighted
    echo "╔" . str_repeat('═', $receiptWidth - 2) . "╗\n";
    $totalText = "💰 TOTAL: " . formatCurrency($payment['amount'], $currency) . " 💰";
    echo "║" . centerText($totalText, $receiptWidth - 2) . "║\n";
    echo "╚" . str_repeat('═', $receiptWidth - 2) . "╝\n";

    echo "\n";

    // Additional info if available
    if ($payment['transaction_id'] || $payment['processed_by_name']) {
        echo str_repeat('─', $receiptWidth) . "\n";
        if ($payment['transaction_id']) {
            echo "Transaction ID: " . $payment['transaction_id'] . "\n";
        }
        if ($payment['processed_by_name']) {
            echo "Processed by: " . $payment['processed_by_name'] . "\n";
        }
        echo str_repeat('─', $receiptWidth) . "\n";
    }

    echo "\n";

    // Footer with style
    echo centerText("🎉 THANK YOU! 🎉") . "\n";
    echo centerText("💪 Stay Strong! 💪") . "\n";

    echo "\n";
    echo centerText(date('M j, Y g:i A')) . "\n";

    echo "\n";
    echo str_repeat('═', $receiptWidth) . "\n";
    echo centerText("🏋️ " . strtoupper($gymName) . " 🏋️") . "\n";
    echo str_repeat('═', $receiptWidth) . "\n";

    echo "\n\n\n"; // Extra lines for paper cutting

} catch (Exception $e) {
    http_response_code(500);
    exit('Error generating thermal receipt: ' . $e->getMessage());
}
