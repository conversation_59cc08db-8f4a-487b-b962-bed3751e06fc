<?php
/**
 * Local Admin Credentials
 * MyGym Management System
 *
 * These credentials are stored locally in the code, not in the database
 * This ensures the admin can always login even if the database is corrupted
 *
 * IMPORTANT:
 * - Change the default passwords before deploying to production
 * - Use strong passwords with mixed case, numbers, and symbols
 * - Update the 'name' field to reflect your gym's actual administrator
 * - Change email addresses to your actual domain
 *
 * To customize:
 * 1. Change 'name' to the actual administrator's name
 * 2. Update 'email' to use your gym's domain
 * 3. Set strong, unique passwords
 * 4. You can add/remove admin accounts as needed
 */

return [
    'admin' => [
        'name' => 'MyGym Administrator',
        'email' => '<EMAIL>',
        'password' => 'MyGym2024!Admin', // Plain text - will be hashed during verification
        'role' => 'admin',
        'is_active' => true,
        'is_local' => true // Flag to indicate this is a local admin
    ],

    // You can add more local admin accounts here if needed
    'owner' => [
        'name' => 'Gym Owner',
        'email' => '<EMAIL>',
        'password' => 'MyGym2024!Owner',
        'role' => 'admin',
        'is_active' => true,
        'is_local' => true
    ]
];
?>
