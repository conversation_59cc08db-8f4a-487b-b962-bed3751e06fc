<?php
/**
 * Database Setup Class
 * MyGym Management System
 * 
 * Ensures all required tables exist for the application
 */

class DatabaseSetup {
    
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Create all required tables for the settings page and application
     */
    public function createAllTables() {
        $tables = $this->getTableDefinitions();
        $results = [];
        
        foreach ($tables as $tableName => $sql) {
            try {
                $exists = $this->tableExists($tableName);
                if (!$exists) {
                    $this->db->query($sql);
                    $results[$tableName] = 'created';
                } else {
                    $results[$tableName] = 'exists';
                }
            } catch (Exception $e) {
                $results[$tableName] = 'error: ' . $e->getMessage();
            }
        }

        // Run migrations for existing tables
        try {
            // Add avatar column to members table if it doesn't exist
            if ($this->tableExists('members')) {
                $columns = $this->db->fetchAll("SHOW COLUMNS FROM members LIKE 'avatar'");
                if (empty($columns)) {
                    $this->db->query("ALTER TABLE members ADD COLUMN avatar varchar(255) DEFAULT NULL AFTER emergency_contact_phone");
                    $results['members_avatar_migration'] = 'added avatar column';
                }
            }
        } catch (Exception $e) {
            $results['members_avatar_migration'] = 'error: ' . $e->getMessage();
        }

        return $results;
    }
    
    /**
     * Check if a table exists
     */
    private function tableExists($tableName) {
        $tables = $this->db->fetchAll("SHOW TABLES LIKE ?", [$tableName]);
        return !empty($tables);
    }
    
    /**
     * Initialize default settings
     */
    public function initializeDefaultSettings() {
        if (!$this->tableExists('settings')) {
            throw new Exception('Settings table does not exist');
        }
        
        $defaultSettings = [
            'gym_name' => 'MyGym Fitness Center',
            'gym_address' => '',
            'gym_phone' => '',
            'gym_email' => '',
            'gym_website' => '',
            'currency' => 'USD',
            'date_format' => 'Y-m-d',
            'timezone' => 'UTC',
            'language' => 'en',
            'smtp_host' => '',
            'smtp_port' => 587,
            'smtp_user' => '',
            'smtp_pass' => '',
            'smtp_encryption' => 'tls',
            'sms_api_key' => '',
            'sms_sender' => '',
            'backup_enabled' => false,
            'backup_frequency' => 'daily',
            'maintenance_mode' => false,
            'registration_enabled' => true,
            'max_file_size' => 5242880,
            'password_min_length' => 6,
            'enable_2fa' => false,
            'log_retention_days' => 90
        ];
        
        $added = 0;
        foreach ($defaultSettings as $key => $value) {
            $existing = $this->db->fetch("SELECT id FROM settings WHERE `key` = ?", [$key]);
            if (!$existing) {
                $this->db->query("INSERT INTO settings (`key`, `value`) VALUES (?, ?)", [$key, $value]);
                $added++;
            }
        }
        
        return $added;
    }
    
    /**
     * Get all table definitions
     */
    private function getTableDefinitions() {
        return [
            'settings' => "
                CREATE TABLE IF NOT EXISTS `settings` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `key` varchar(255) NOT NULL,
                    `value` text,
                    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `unique_key` (`key`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'users' => "
                CREATE TABLE IF NOT EXISTS `users` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `email` varchar(255) NOT NULL,
                    `password` varchar(255) NOT NULL,
                    `role` enum('admin','staff','member') DEFAULT 'member',
                    `remember_token` varchar(100) DEFAULT NULL,
                    `email_verified_at` timestamp NULL DEFAULT NULL,
                    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `unique_email` (`email`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'activity_logs' => "
                CREATE TABLE IF NOT EXISTS `activity_logs` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `user_id` int(11) DEFAULT NULL,
                    `action` varchar(255) NOT NULL,
                    `table_name` varchar(100) DEFAULT NULL,
                    `record_id` int(11) DEFAULT NULL,
                    `old_data` text,
                    `new_data` text,
                    `ip_address` varchar(45) DEFAULT NULL,
                    `user_agent` text,
                    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    KEY `idx_user_id` (`user_id`),
                    KEY `idx_table_record` (`table_name`, `record_id`),
                    KEY `idx_created_at` (`created_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'members' => "
                CREATE TABLE IF NOT EXISTS `members` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `member_id` varchar(50) NOT NULL,
                    `first_name` varchar(100) NOT NULL,
                    `last_name` varchar(100) NOT NULL,
                    `email` varchar(255) DEFAULT NULL,
                    `phone` varchar(20) DEFAULT NULL,
                    `address` text,
                    `date_of_birth` date DEFAULT NULL,
                    `gender` enum('male','female','other') DEFAULT NULL,
                    `emergency_contact_name` varchar(255) DEFAULT NULL,
                    `emergency_contact_phone` varchar(20) DEFAULT NULL,
                    `avatar` varchar(255) DEFAULT NULL,
                    `membership_start_date` date DEFAULT NULL,
                    `membership_end_date` date DEFAULT NULL,
                    `status` enum('active','inactive','suspended','expired') DEFAULT 'active',
                    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `unique_member_id` (`member_id`),
                    KEY `idx_status` (`status`),
                    KEY `idx_membership_dates` (`membership_start_date`, `membership_end_date`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'plans' => "
                CREATE TABLE IF NOT EXISTS `plans` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `description` text,
                    `price` decimal(10,2) NOT NULL,
                    `duration_months` int(11) NOT NULL,
                    `features` text,
                    `is_active` tinyint(1) DEFAULT 1,
                    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    KEY `idx_active` (`is_active`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'payments' => "
                CREATE TABLE IF NOT EXISTS `payments` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `receipt_number` varchar(100) NOT NULL,
                    `member_id` int(11) NOT NULL,
                    `plan_id` int(11) DEFAULT NULL,
                    `amount` decimal(10,2) NOT NULL,
                    `payment_method` enum('cash','card','online','bank_transfer') NOT NULL,
                    `payment_date` date NOT NULL,
                    `payment_for` varchar(255) DEFAULT NULL,
                    `notes` text,
                    `processed_by` int(11) DEFAULT NULL,
                    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `unique_receipt` (`receipt_number`),
                    KEY `idx_member_id` (`member_id`),
                    KEY `idx_plan_id` (`plan_id`),
                    KEY `idx_payment_date` (`payment_date`),
                    KEY `idx_processed_by` (`processed_by`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            'backups' => "
                CREATE TABLE IF NOT EXISTS `backups` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `filename` varchar(255) NOT NULL,
                    `filepath` varchar(500) NOT NULL,
                    `description` text,
                    `file_size` bigint(20) DEFAULT 0,
                    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                    `last_restored` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `idx_filename` (`filename`),
                    KEY `idx_created_at` (`created_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            "
        ];
    }
    
    /**
     * Ensure all required tables exist (called automatically)
     */
    public static function ensureTablesExist() {
        try {
            $setup = new self();
            $results = $setup->createAllTables();
            
            // Initialize settings if settings table was created
            if (isset($results['settings']) && $results['settings'] === 'created') {
                $setup->initializeDefaultSettings();
            }
            
            return $results;
        } catch (Exception $e) {
            error_log('DatabaseSetup error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get table status information
     */
    public function getTableStatus() {
        $tables = array_keys($this->getTableDefinitions());
        $status = [];
        
        foreach ($tables as $table) {
            try {
                $exists = $this->tableExists($table);
                if ($exists) {
                    $count = $this->db->fetch("SELECT COUNT(*) as count FROM $table")['count'];
                    $status[$table] = [
                        'exists' => true,
                        'records' => $count
                    ];
                } else {
                    $status[$table] = [
                        'exists' => false,
                        'records' => 0
                    ];
                }
            } catch (Exception $e) {
                $status[$table] = [
                    'exists' => false,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $status;
    }
}
?>
