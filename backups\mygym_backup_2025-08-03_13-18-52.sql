-- My<PERSON>ym Backup 2025-08-03 13:18:52
SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS `activity_logs`;
CREATE TABLE `activity_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `action` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `table_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `record_id` int DEFAULT NULL,
  `old_values` text COLLATE utf8mb4_unicode_ci,
  `new_values` text COLLATE utf8mb4_unicode_ci,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  <PERSON><PERSON><PERSON><PERSON> (`id`),
  <PERSON><PERSON>Y `user_id` (`user_id`),
  <PERSON><PERSON><PERSON> `created_at` (`created_at`),
  CONSTRAINT `activity_logs_user_fk` FOREIG<PERSON> KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `checkins`;
CREATE TABLE `checkins` (
  `id` int NOT NULL AUTO_INCREMENT,
  `member_id` int NOT NULL,
  `checkin_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `checkout_time` timestamp NULL DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `member_id` (`member_id`),
  KEY `checkin_time` (`checkin_time`),
  CONSTRAINT `checkins_member_fk` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `email_campaigns`;
CREATE TABLE `email_campaigns` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `template` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `trigger_days` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `email_campaigns` VALUES ('1', 'Welcome Email', 'Welcome to {gym_name}!', 'Welcome to our gym family! We are excited to have you on board.', '1', '1', '2025-08-03 14:13:20');
INSERT INTO `email_campaigns` VALUES ('2', 'Workout Tips', 'Your Fitness Journey - Week 1 Tips', 'Here are some great workout tips to get you started on your fitness journey.', '3', '1', '2025-08-03 14:13:20');
INSERT INTO `email_campaigns` VALUES ('3', 'Plan Upgrade Offer', 'Special Upgrade Offer Just for You!', 'We have a special offer to upgrade your membership plan with exclusive benefits.', '5', '1', '2025-08-03 14:13:20');

DROP TABLE IF EXISTS `email_queue`;
CREATE TABLE `email_queue` (
  `id` int NOT NULL AUTO_INCREMENT,
  `member_id` int NOT NULL,
  `campaign_id` int NOT NULL,
  `scheduled_at` timestamp NOT NULL,
  `sent_at` timestamp NULL DEFAULT NULL,
  `status` enum('pending','sent','failed') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `error_message` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `member_id` (`member_id`),
  KEY `campaign_id` (`campaign_id`),
  KEY `scheduled_at` (`scheduled_at`),
  KEY `status` (`status`),
  CONSTRAINT `email_queue_campaign_fk` FOREIGN KEY (`campaign_id`) REFERENCES `email_campaigns` (`id`) ON DELETE CASCADE,
  CONSTRAINT `email_queue_member_fk` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `equipment`;
CREATE TABLE `equipment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `brand` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `model` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `serial_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `purchase_date` date DEFAULT NULL,
  `purchase_price` decimal(10,2) DEFAULT NULL,
  `condition_status` enum('excellent','good','fair','poor','out_of_order') COLLATE utf8mb4_unicode_ci DEFAULT 'good',
  `maintenance_date` date DEFAULT NULL,
  `warranty_expiry` date DEFAULT NULL,
  `location` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `members`;
CREATE TABLE `members` (
  `id` int NOT NULL AUTO_INCREMENT,
  `member_id` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `first_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female','other') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` text COLLATE utf8mb4_unicode_ci,
  `emergency_contact` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `emergency_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `id_document` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `plan_id` int DEFAULT NULL,
  `trainer_id` int DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `status` enum('active','expired','suspended','cancelled') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `member_id` (`member_id`),
  KEY `plan_id` (`plan_id`),
  KEY `trainer_id` (`trainer_id`),
  KEY `status` (`status`),
  KEY `end_date` (`end_date`),
  CONSTRAINT `members_plan_fk` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`) ON DELETE SET NULL,
  CONSTRAINT `members_trainer_fk` FOREIGN KEY (`trainer_id`) REFERENCES `trainers` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `payments`;
CREATE TABLE `payments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `member_id` int NOT NULL,
  `plan_id` int NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_method` enum('cash','card','bank_transfer','online') COLLATE utf8mb4_unicode_ci DEFAULT 'cash',
  `transaction_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `receipt_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payment_date` date NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `processed_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `receipt_number` (`receipt_number`),
  KEY `member_id` (`member_id`),
  KEY `plan_id` (`plan_id`),
  KEY `processed_by` (`processed_by`),
  KEY `payment_date` (`payment_date`),
  CONSTRAINT `payments_member_fk` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  CONSTRAINT `payments_plan_fk` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`),
  CONSTRAINT `payments_user_fk` FOREIGN KEY (`processed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `plans`;
CREATE TABLE `plans` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `duration_months` int NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `features` text COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `plans` VALUES ('1', '1 Month Plan', 'Perfect for trying out our gym facilities', '1', '30.00', 'Access to all equipment,Group classes,Locker access', '1', '2025-08-03 14:13:20', '2025-08-03 14:13:20');
INSERT INTO `plans` VALUES ('2', '3 Months Plan', 'Great value for short-term commitment', '3', '80.00', 'Access to all equipment,Group classes,Locker access,1 personal training session', '1', '2025-08-03 14:13:20', '2025-08-03 14:13:20');
INSERT INTO `plans` VALUES ('3', '6 Months Plan', 'Best value for serious fitness goals', '6', '150.00', 'Access to all equipment,Group classes,Locker access,2 personal training sessions,Nutrition consultation', '1', '2025-08-03 14:13:20', '2025-08-03 14:13:20');
INSERT INTO `plans` VALUES ('4', '1 Year Plan', 'Ultimate fitness commitment with maximum savings', '12', '280.00', 'Access to all equipment,Group classes,Locker access,4 personal training sessions,Nutrition consultation,Priority booking', '1', '2025-08-03 14:13:20', '2025-08-03 14:13:20');

DROP TABLE IF EXISTS `settings`;
CREATE TABLE `settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `settings` VALUES ('1', 'gym_name', 'MY GYM', '2025-08-03 14:13:21', '2025-08-03 14:18:47');
INSERT INTO `settings` VALUES ('2', 'site_url', 'http://localhost/mygym', '2025-08-03 14:13:21', '2025-08-03 14:13:21');
INSERT INTO `settings` VALUES ('3', 'currency', 'USD', '2025-08-03 14:13:21', '2025-08-03 14:18:47');
INSERT INTO `settings` VALUES ('4', 'timezone', 'UTC', '2025-08-03 14:13:21', '2025-08-03 14:13:21');
INSERT INTO `settings` VALUES ('5', 'installation_date', '2025-08-03 13:13:21', '2025-08-03 14:13:21', '2025-08-03 14:13:21');
INSERT INTO `settings` VALUES ('6', 'version', '2.0', '2025-08-03 14:13:21', '2025-08-03 14:13:21');
INSERT INTO `settings` VALUES ('8', 'gym_email', '<EMAIL>', '2025-08-03 14:18:31', '2025-08-03 14:18:47');
INSERT INTO `settings` VALUES ('9', 'gym_phone', '0640117969', '2025-08-03 14:18:31', '2025-08-03 14:18:47');
INSERT INTO `settings` VALUES ('10', 'gym_address', '', '2025-08-03 14:18:31', '2025-08-03 14:18:47');
INSERT INTO `settings` VALUES ('12', 'language', 'en', '2025-08-03 14:18:31', '2025-08-03 14:18:47');
INSERT INTO `settings` VALUES ('13', 'smtp_host', '', '2025-08-03 14:18:31', '2025-08-03 14:18:47');
INSERT INTO `settings` VALUES ('14', 'smtp_port', '587', '2025-08-03 14:18:31', '2025-08-03 14:18:47');
INSERT INTO `settings` VALUES ('15', 'smtp_user', '<EMAIL>', '2025-08-03 14:18:31', '2025-08-03 14:18:47');
INSERT INTO `settings` VALUES ('16', 'smtp_pass', '', '2025-08-03 14:18:31', '2025-08-03 14:18:47');
INSERT INTO `settings` VALUES ('17', 'smtp_encryption', 'tls', '2025-08-03 14:18:31', '2025-08-03 14:18:47');
INSERT INTO `settings` VALUES ('18', 'sms_provider', 'demo', '2025-08-03 14:18:31', '2025-08-03 14:18:47');
INSERT INTO `settings` VALUES ('19', 'sms_api_key', '', '2025-08-03 14:18:31', '2025-08-03 14:18:47');
INSERT INTO `settings` VALUES ('20', 'sms_sender_id', '', '2025-08-03 14:18:31', '2025-08-03 14:18:47');
INSERT INTO `settings` VALUES ('21', 'sms_server_url', '', '2025-08-03 14:18:31', '2025-08-03 14:18:47');
INSERT INTO `settings` VALUES ('22', 'sms_username', 'admin', '2025-08-03 14:18:31', '2025-08-03 14:18:47');

DROP TABLE IF EXISTS `trainers`;
CREATE TABLE `trainers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `specialty` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bio` text COLLATE utf8mb4_unicode_ci,
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `hourly_rate` decimal(10,2) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` enum('admin','manager','receptionist') COLLATE utf8mb4_unicode_ci DEFAULT 'receptionist',
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `last_login` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `users` VALUES ('1', 'bilal abbou', '<EMAIL>', '$2y$10$7bqbG2MK0Pk1Z1.cxfwJR.Ez36UU2jhAdEsC2vSawacFREPwphpq2', 'admin', NULL, '1', '2025-08-03 13:13:33', NULL, '2025-08-03 14:13:21', '2025-08-03 14:13:33');

SET FOREIGN_KEY_CHECKS = 1;
