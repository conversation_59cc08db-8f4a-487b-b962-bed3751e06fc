<?php
/**
 * Receipt Page
 * MyGym Management System
 */

session_start();
require_once '../config/database.php';
require_once '../includes/auth.php';

// Check authentication
Auth::requireAuth();

// Get receipt number or payment ID
$receiptNumber = $_GET['receipt'] ?? null;
$paymentId = $_GET['id'] ?? null;

if (!$receiptNumber && !$paymentId) {
    header('Location: payments.php');
    exit;
}

try {
    $db = Database::getInstance();
    
    // Build query based on what we have
    if ($receiptNumber) {
        $payment = $db->fetch("
            SELECT p.*, 
                   CONCAT(m.first_name, ' ', m.last_name) as member_name,
                   m.member_id,
                   m.phone,
                   m.email,
                   pl.name as plan_name,
                   pl.duration_months,
                   u.name as processed_by_name
            FROM payments p
            JOIN members m ON p.member_id = m.id
            LEFT JOIN plans pl ON p.plan_id = pl.id
            LEFT JOIN users u ON p.processed_by = u.id
            WHERE p.receipt_number = ?
        ", [$receiptNumber]);
    } else {
        $payment = $db->fetch("
            SELECT p.*, 
                   CONCAT(m.first_name, ' ', m.last_name) as member_name,
                   m.member_id,
                   m.phone,
                   m.email,
                   pl.name as plan_name,
                   pl.duration_months,
                   u.name as processed_by_name
            FROM payments p
            JOIN members m ON p.member_id = m.id
            LEFT JOIN plans pl ON p.plan_id = pl.id
            LEFT JOIN users u ON p.processed_by = u.id
            WHERE p.id = ?
        ", [$paymentId]);
    }
    
    if (!$payment) {
        $error = 'Receipt not found';
        // Add debug information
        if ($receiptNumber) {
            $error .= " (Receipt Number: " . htmlspecialchars($receiptNumber) . ")";
        } elseif ($paymentId) {
            $error .= " (Payment ID: " . htmlspecialchars($paymentId) . ")";
        }
    }
    
    // Get gym settings
    try {
        $gymName = Config::get('gym_name', 'MyGym');
        $gymAddress = Config::get('gym_address', '');
        $gymPhone = Config::get('gym_phone', '');
        $gymEmail = Config::get('gym_email', '');
        $currency = Config::get('currency', 'USD');
    } catch (Exception $configError) {
        // Fallback to defaults if Config fails
        $gymName = 'MyGym';
        $gymAddress = '';
        $gymPhone = '';
        $gymEmail = '';
        $currency = 'USD';
    }
    
    // Format currency
    function formatCurrency($amount, $currency = 'USD') {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'INR' => '₹',
            'JPY' => '¥'
        ];
        
        $symbol = $symbols[$currency] ?? $currency . ' ';
        return $symbol . number_format($amount ?? 0, 2);
    }
    
} catch (Exception $e) {
    $error = 'Error retrieving receipt details';
}

$pageTitle = 'Receipt';
include '../includes/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Receipt</h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                <?php if (isset($payment)): ?>
                    Receipt #<?= htmlspecialchars($payment['receipt_number']) ?>
                <?php else: ?>
                    Receipt Details
                <?php endif; ?>
            </p>
        </div>
        
        <div class="mt-4 sm:mt-0 flex space-x-3">
            <a href="payments.php" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition duration-200">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Payments
            </a>
            
            <?php if (isset($payment)): ?>
            <button onclick="viewReceipt(<?= $payment['id'] ?>)"
                    class="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg transition duration-200">
                <i class="fas fa-eye mr-2"></i>
                View Receipt
            </button>
            
            <button onclick="showPrintOptions(<?= $payment['id'] ?>)"
                    class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                <i class="fas fa-print mr-2"></i>
                Print Receipt
            </button>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (isset($error)): ?>
    <!-- Error Message -->
    <div class="bg-red-50 border border-red-200 rounded-2xl p-6">
        <div class="flex">
            <i class="fas fa-exclamation-circle text-red-400 mt-0.5 mr-3"></i>
            <div>
                <h3 class="text-red-800 font-medium">Receipt Not Found</h3>
                <p class="text-red-700 mt-1"><?= htmlspecialchars($error) ?></p>
            </div>
        </div>
    </div>
    
    <!-- Search Form -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Search for Receipt</h3>
        
        <form method="GET" class="space-y-4">
            <div>
                <label for="receipt" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Receipt Number
                </label>
                <input type="text" 
                       id="receipt" 
                       name="receipt" 
                       value="<?= htmlspecialchars($receiptNumber ?? '') ?>"
                       placeholder="Enter receipt number (e.g., RCP20241218001)"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
            </div>
            
            <button type="submit" 
                    class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                <i class="fas fa-search mr-2"></i>
                Search Receipt
            </button>
        </form>
    </div>
    
    <?php elseif (isset($payment)): ?>
    <!-- Receipt Details -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-600">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                        <i class="fas fa-receipt mr-2 text-blue-600"></i>
                        Payment Receipt
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                        Receipt #<?= htmlspecialchars($payment['receipt_number']) ?>
                    </p>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold text-green-600">
                        <?= formatCurrency($payment['amount'], $currency) ?>
                    </div>
                    <div class="text-sm text-gray-500">
                        <?= date('F j, Y', strtotime($payment['payment_date'])) ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Member Information -->
                <div>
                    <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                        <i class="fas fa-user mr-2 text-blue-600"></i>
                        Member Information
                    </h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Name:</span>
                            <span class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($payment['member_name']) ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Member ID:</span>
                            <span class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($payment['member_id']) ?></span>
                        </div>
                        <?php if ($payment['phone']): ?>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Phone:</span>
                            <span class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($payment['phone']) ?></span>
                        </div>
                        <?php endif; ?>
                        <?php if ($payment['email']): ?>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Email:</span>
                            <span class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($payment['email']) ?></span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Membership Details -->
                <div>
                    <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                        <i class="fas fa-dumbbell mr-2 text-blue-600"></i>
                        Membership Details
                    </h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Plan:</span>
                            <span class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($payment['plan_name']) ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Duration:</span>
                            <span class="font-medium text-gray-900 dark:text-white"><?= $payment['duration_months'] ?> month(s)</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Start Date:</span>
                            <span class="font-medium text-gray-900 dark:text-white"><?= date('F j, Y', strtotime($payment['start_date'])) ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">End Date:</span>
                            <span class="font-medium text-gray-900 dark:text-white"><?= date('F j, Y', strtotime($payment['end_date'])) ?></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Payment Information -->
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                            <i class="fas fa-credit-card mr-2 text-blue-600"></i>
                            Payment Information
                        </h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Method:</span>
                                <span class="font-medium text-gray-900 dark:text-white"><?= ucfirst(str_replace('_', ' ', $payment['payment_method'])) ?></span>
                            </div>
                            <?php if ($payment['transaction_id']): ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Transaction ID:</span>
                                <span class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($payment['transaction_id']) ?></span>
                            </div>
                            <?php endif; ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Processed By:</span>
                                <span class="font-medium text-gray-900 dark:text-white">
                                    <?php if ($payment['processed_by_name']): ?>
                                        <?= htmlspecialchars($payment['processed_by_name']) ?>
                                    <?php elseif (strpos($payment['notes'], 'Local Admin') !== false): ?>
                                        Local Admin
                                    <?php else: ?>
                                        System
                                    <?php endif; ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional Information -->
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                            <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                            Additional Information
                        </h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Created:</span>
                                <span class="font-medium text-gray-900 dark:text-white"><?= date('F j, Y g:i A', strtotime($payment['created_at'])) ?></span>
                            </div>
                            <?php if ($payment['notes'] && !strpos($payment['notes'], 'Local Admin')): ?>
                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Notes:</span>
                                <div class="mt-1 p-2 bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded text-yellow-800 dark:text-yellow-200">
                                    <?= htmlspecialchars(str_replace(' (Local Admin)', '', $payment['notes'])) ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
