<?php
/**
 * Configuration Management Class
 * MyGym Management System
 */

class Config {
    
    private static $cache = [];
    
    /**
     * Get a configuration value
     */
    public static function get($key, $default = null) {
        // Check cache first
        if (isset(self::$cache[$key])) {
            return self::$cache[$key];
        }
        
        try {
            $db = Database::getInstance();
            
            $result = $db->fetch("SELECT value FROM settings WHERE `key` = ?", [$key]);
            
            if ($result) {
                $value = $result['value'];
                
                // Try to decode JSON values
                $decoded = json_decode($value, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $value = $decoded;
                }
                
                // Cache the value
                self::$cache[$key] = $value;
                
                return $value;
            }
            
        } catch (Exception $e) {
            // If database is not available, return default
        }
        
        return $default;
    }
    
    /**
     * Set a configuration value
     */
    public static function set($key, $value) {
        try {
            $db = Database::getInstance();

            // Encode arrays/objects as JSON
            if (is_array($value) || is_object($value)) {
                $value = json_encode($value);
            }

            // Use UPSERT operation for better reliability
            $stmt = $db->query("
                INSERT INTO settings (`key`, `value`)
                VALUES (?, ?)
                ON DUPLICATE KEY UPDATE `value` = VALUES(`value`), `updated_at` = CURRENT_TIMESTAMP
            ", [$key, $value]);

            // Check if the operation was successful
            if ($stmt && $stmt->rowCount() >= 0) {
                // Verify the setting was actually saved
                $saved = $db->fetch("SELECT `value` FROM settings WHERE `key` = ?", [$key]);
                if ($saved && $saved['value'] == $value) {
                    // Update cache
                    $decoded = json_decode($value, true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        self::$cache[$key] = $decoded;
                    } else {
                        self::$cache[$key] = $value;
                    }
                    return true;
                } else {
                    error_log("Config::set verification failed for key '$key'");
                    return false;
                }
            } else {
                error_log("Config::set query failed for key '$key'");
                return false;
            }

        } catch (Exception $e) {
            error_log('Config::set error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get multiple configuration values
     */
    public static function getMultiple($keys) {
        $result = [];
        
        foreach ($keys as $key) {
            $result[$key] = self::get($key);
        }
        
        return $result;
    }
    
    /**
     * Set multiple configuration values
     */
    public static function setMultiple($data) {
        $success = true;
        
        foreach ($data as $key => $value) {
            if (!self::set($key, $value)) {
                $success = false;
            }
        }
        
        return $success;
    }
    
    /**
     * Delete a configuration value
     */
    public static function delete($key) {
        try {
            $db = Database::getInstance();
            
            $db->delete('settings', '`key` = ?', [$key]);
            
            // Remove from cache
            unset(self::$cache[$key]);
            
            return true;
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get all configuration values
     */
    public static function all() {
        try {
            $db = Database::getInstance();
            
            $settings = $db->fetchAll("SELECT `key`, value FROM settings");
            
            $result = [];
            
            foreach ($settings as $setting) {
                $value = $setting['value'];
                
                // Try to decode JSON values
                $decoded = json_decode($value, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $value = $decoded;
                }
                
                $result[$setting['key']] = $value;
            }
            
            return $result;
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Clear configuration cache
     */
    public static function clearCache() {
        self::$cache = [];
    }
    
    /**
     * Get default configuration values
     */
    public static function getDefaults() {
        return [
            'gym_name' => 'MyGym',
            'gym_address' => '',
            'gym_phone' => '',
            'gym_email' => '',
            'gym_website' => '',
            'currency' => 'USD',
            'date_format' => 'Y-m-d',
            'time_format' => 'H:i',
            'timezone' => 'UTC',
            'language' => 'en',
            'smtp_host' => '',
            'smtp_port' => 587,
            'smtp_user' => '',
            'smtp_pass' => '',
            'smtp_encryption' => 'tls',
            'sms_api_key' => '',
            'sms_sender' => '',
            'backup_enabled' => false,
            'backup_frequency' => 'daily',
            'maintenance_mode' => false,
            'registration_enabled' => true,
            'max_file_size' => 5242880, // 5MB
            'allowed_file_types' => 'jpg,jpeg,png,gif,pdf,doc,docx',
            'session_timeout' => 3600, // 1 hour
            'password_min_length' => 6,
            'enable_2fa' => false,
            'log_retention_days' => 90
        ];
    }
    
    /**
     * Initialize default settings
     */
    public static function initializeDefaults() {
        $defaults = self::getDefaults();
        
        foreach ($defaults as $key => $value) {
            // Only set if not already exists
            if (self::get($key) === null) {
                self::set($key, $value);
            }
        }
    }
}
