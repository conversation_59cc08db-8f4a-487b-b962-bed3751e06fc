<?php
session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    echo '{"success":false,"message":"Admin access required"}';
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';

if ($action === 'create') {
    try {
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "mygym_backup_{$timestamp}.sql";
        $backupDir = dirname(__DIR__) . '/backups';
        
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        $filepath = $backupDir . '/' . $filename;
        
        $sql = "-- MyGym Backup " . date('Y-m-d H:i:s') . "\n";
        $sql .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";
        
        require_once dirname(__DIR__) . '/config/database.php';
        $db = Database::getInstance();
        
        $tables = $db->fetchAll("SHOW TABLES");
        
        foreach ($tables as $table) {
            $tableName = array_values($table)[0];
            
            $createResult = $db->fetch("SHOW CREATE TABLE `{$tableName}`");
            $sql .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
            $sql .= $createResult['Create Table'] . ";\n\n";
            
            $rows = $db->fetchAll("SELECT * FROM `{$tableName}`");
            foreach ($rows as $row) {
                $values = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $values[] = 'NULL';
                    } else {
                        $values[] = "'" . str_replace("'", "''", $value) . "'";
                    }
                }
                $sql .= "INSERT INTO `{$tableName}` VALUES (" . implode(', ', $values) . ");\n";
            }
            $sql .= "\n";
        }
        
        $sql .= "SET FOREIGN_KEY_CHECKS = 1;\n";
        
        file_put_contents($filepath, $sql);
        
        echo '{"success":true,"message":"Backup created successfully","filename":"' . $filename . '","size":"' . round(filesize($filepath)/1024,2) . ' KB"}';
        
    } catch (Exception $e) {
        echo '{"success":false,"message":"Error: ' . addslashes($e->getMessage()) . '"}';
    }
    
} elseif ($action === 'list') {
    try {
        $backupDir = dirname(__DIR__) . '/backups';
        $backups = [];
        
        if (is_dir($backupDir)) {
            $files = glob($backupDir . '/*.sql');
            foreach ($files as $file) {
                $filename = basename($file);
                $backups[] = [
                    'filename' => $filename,
                    'size' => round(filesize($file) / 1024, 2) . ' KB',
                    'date' => date('M j, Y g:i A', filemtime($file)),
                    'timestamp' => filemtime($file)
                ];
            }
            
            usort($backups, function($a, $b) {
                return $b['timestamp'] - $a['timestamp'];
            });
        }
        
        echo '{"success":true,"backups":' . json_encode($backups) . ',"info":{"count":' . count($backups) . ',"total_size":"' . count($backups) . ' files"}}';
        
    } catch (Exception $e) {
        echo '{"success":false,"message":"Error: ' . addslashes($e->getMessage()) . '"}';
    }
    
} elseif ($action === 'delete') {
    try {
        $filename = $_POST['filename'] ?? '';
        if (empty($filename)) {
            echo '{"success":false,"message":"Filename required"}';
            exit;
        }
        
        $backupDir = dirname(__DIR__) . '/backups';
        $filepath = $backupDir . '/' . $filename;
        
        if (file_exists($filepath) && unlink($filepath)) {
            echo '{"success":true,"message":"Backup deleted"}';
        } else {
            echo '{"success":false,"message":"Delete failed"}';
        }
        
    } catch (Exception $e) {
        echo '{"success":false,"message":"Error: ' . addslashes($e->getMessage()) . '"}';
    }
    
} elseif ($action === 'download') {
    try {
        $filename = $_GET['filename'] ?? '';
        if (empty($filename)) {
            echo '{"success":false,"message":"Filename required"}';
            exit;
        }
        
        $backupDir = dirname(__DIR__) . '/backups';
        $filepath = $backupDir . '/' . $filename;
        
        if (file_exists($filepath)) {
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . filesize($filepath));
            readfile($filepath);
            exit;
        } else {
            echo '{"success":false,"message":"File not found"}';
        }
        
    } catch (Exception $e) {
        echo '{"success":false,"message":"Error: ' . addslashes($e->getMessage()) . '"}';
    }
    
} elseif ($action === 'restore') {
    try {
        $filename = $_POST['filename'] ?? '';
        if (empty($filename)) {
            echo '{"success":false,"message":"Filename required"}';
            exit;
        }
        
        $backupDir = dirname(__DIR__) . '/backups';
        $filepath = $backupDir . '/' . $filename;
        
        if (!file_exists($filepath)) {
            echo '{"success":false,"message":"Backup file not found"}';
            exit;
        }
        
        $sql = file_get_contents($filepath);
        require_once dirname(__DIR__) . '/config/database.php';
        $db = Database::getInstance();
        
        $statements = explode(';', $sql);
        $executed = 0;
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && strpos($statement, '--') !== 0) {
                $db->query($statement);
                $executed++;
            }
        }
        
        echo '{"success":true,"message":"Database restored successfully","statements_executed":' . $executed . '}';
        
    } catch (Exception $e) {
        echo '{"success":false,"message":"Error: ' . addslashes($e->getMessage()) . '"}';
    }
    
} else {
    echo '{"success":false,"message":"Invalid action"}';
}

exit;
?>
