<?php
/**
 * Thermal Receipt Printer - HTML Version with Cool Design
 * MyGym Management System
 */

session_start();

// Check authentication
if (!isset($_SESSION['user_id']) && !isset($_SESSION['local_admin_username'])) {
    http_response_code(401);
    exit('Unauthorized');
}

// Get payment ID
$paymentId = $_GET['id'] ?? null;
if (!$paymentId || !is_numeric($paymentId)) {
    http_response_code(400);
    exit('Invalid payment ID');
}

$autoPrint = isset($_GET['auto_print']) && $_GET['auto_print'] == '1';

try {
    // Include required files
    require_once '../config/database.php';
    require_once '../includes/auth.php';

    $db = Database::getInstance();

    // Get payment details (NO sensitive information for security)
    $payment = $db->fetch("
        SELECT p.*,
               CONCAT(m.first_name, ' ', m.last_name) as member_name,
               m.member_id,
               pl.name as plan_name, pl.duration_months,
               u.name as processed_by_name
        FROM payments p
        LEFT JOIN members m ON p.member_id = m.id
        LEFT JOIN plans pl ON p.plan_id = pl.id
        LEFT JOIN users u ON p.processed_by = u.id
        WHERE p.id = ?
    ", [$paymentId]);

    if (!$payment) {
        http_response_code(404);
        exit('Payment not found');
    }

    // Get gym settings with fallbacks
    try {
        $gymName = Config::get('gym_name', 'MyGym');
        $gymAddress = Config::get('gym_address', '');
        $gymPhone = Config::get('gym_phone', '');
        $gymEmail = Config::get('gym_email', '');
        $currency = Config::get('currency', 'USD');
    } catch (Exception $configError) {
        // Fallback to defaults if Config/settings table doesn't exist
        $gymName = 'MyGym';
        $gymAddress = '';
        $gymPhone = '';
        $gymEmail = '';
        $currency = 'USD';
    }

} catch (Exception $e) {
    http_response_code(500);
    exit('Error retrieving payment details: ' . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thermal Receipt - <?= htmlspecialchars($payment['receipt_number']) ?></title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;700&display=swap');
        
        @media print {
            @page {
                size: 80mm auto;
                margin: 0;
            }
            body { margin: 0; }
            .no-print { display: none !important; }
        }
        
        body {
            font-family: 'JetBrains Mono', 'Courier New', monospace;
            font-size: 11px;
            line-height: 1.3;
            margin: 0;
            padding: 8px;
            width: 80mm;
            max-width: 80mm;
            background: #000;
            color: #00ff00;
            overflow-x: hidden;
        }
        
        .receipt {
            width: 100%;
            background: #000;
            color: #00ff00;
            border: 2px solid #00ff00;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 0 20px #00ff0050;
        }
        
        .header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 2px dashed #00ff00;
            padding-bottom: 10px;
        }
        
        .gym-name {
            font-size: 14px;
            font-weight: bold;
            color: #00ffff;
            text-shadow: 0 0 10px #00ffff;
            margin-bottom: 5px;
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { text-shadow: 0 0 5px #00ffff, 0 0 10px #00ffff; }
            to { text-shadow: 0 0 10px #00ffff, 0 0 20px #00ffff, 0 0 30px #00ffff; }
        }
        
        .contact-info {
            font-size: 9px;
            color: #ffff00;
            margin: 2px 0;
        }
        
        .receipt-title {
            font-size: 12px;
            font-weight: bold;
            color: #ff00ff;
            text-align: center;
            margin: 10px 0;
            padding: 5px;
            border: 1px solid #ff00ff;
            background: rgba(255, 0, 255, 0.1);
        }
        
        .receipt-number {
            font-size: 11px;
            font-weight: bold;
            text-align: center;
            color: #00ffff;
            background: rgba(0, 255, 255, 0.1);
            padding: 5px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .section {
            margin: 12px 0;
            border: 1px solid #00ff00;
            border-radius: 4px;
            padding: 8px;
            background: rgba(0, 255, 0, 0.05);
        }
        
        .section-title {
            font-weight: bold;
            color: #ffff00;
            text-align: center;
            margin-bottom: 8px;
            font-size: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .row {
            display: flex;
            justify-content: space-between;
            margin: 4px 0;
            font-size: 10px;
        }
        
        .label {
            font-weight: bold;
            color: #00ffff;
        }
        
        .value {
            color: #ffffff;
            text-align: right;
        }
        
        .total {
            font-size: 13px;
            font-weight: bold;
            text-align: center;
            margin: 15px 0;
            padding: 10px;
            border: 2px solid #ffff00;
            background: rgba(255, 255, 0, 0.1);
            color: #ffff00;
            border-radius: 6px;
            animation: pulse 1.5s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }
        
        .footer {
            text-align: center;
            margin-top: 15px;
            border-top: 2px dashed #00ff00;
            padding-top: 10px;
            color: #00ffff;
        }
        
        .thank-you {
            font-size: 11px;
            font-weight: bold;
            color: #ff00ff;
            margin: 8px 0;
        }
        
        .qr-placeholder {
            text-align: center;
            margin: 15px 0;
            font-family: monospace;
            font-size: 8px;
            color: #00ff00;
            line-height: 1;
        }
        
        .ascii-art {
            font-family: monospace;
            font-size: 8px;
            color: #00ffff;
            text-align: center;
            line-height: 1;
            margin: 10px 0;
        }
        
        .emoji {
            font-size: 14px;
            margin: 0 2px;
        }
        
        .no-print {
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            border-radius: 6px;
        }
        
        .print-btn {
            background: linear-gradient(45deg, #00ff00, #00ffff);
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .print-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 0 15px #00ff0080;
        }
        
        .close-btn {
            background: linear-gradient(45deg, #ff0000, #ff6600);
            color: #fff;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .close-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 0 15px #ff000080;
        }
    </style>
</head>
<body>
    <div class="receipt">
        <!-- ASCII Art Header -->
        <div class="ascii-art">
            ╔══════════════════════════════╗<br>
            ║          💪 GYM 💪           ║<br>
            ╚══════════════════════════════╝
        </div>
        
        <!-- Header -->
        <div class="header">
            <div class="gym-name">★ <?= strtoupper(htmlspecialchars($gymName)) ?> ★</div>
            <?php if ($gymAddress): ?>
                <div class="contact-info"><?= htmlspecialchars($gymAddress) ?></div>
            <?php endif; ?>
            <?php if ($gymPhone): ?>
                <div class="contact-info">📞 <?= htmlspecialchars($gymPhone) ?></div>
            <?php endif; ?>
            <?php if ($gymEmail): ?>
                <div class="contact-info">📧 <?= htmlspecialchars($gymEmail) ?></div>
            <?php endif; ?>
        </div>
        
        <!-- Receipt Title -->
        <div class="receipt-title">🧾 PAYMENT RECEIPT 🧾</div>
        <div class="receipt-number"><?= htmlspecialchars($payment['receipt_number']) ?></div>
        
        <!-- Member Information (NO sensitive data) -->
        <div class="section">
            <div class="section-title">👤 Member Info</div>
            <div class="row">
                <span class="label">Name:</span>
                <span class="value"><?= htmlspecialchars($payment['member_name']) ?></span>
            </div>
            <div class="row">
                <span class="label">Member ID:</span>
                <span class="value"><?= htmlspecialchars($payment['member_id']) ?></span>
            </div>
        </div>
        
        <!-- Payment Details -->
        <div class="section">
            <div class="section-title">💳 Payment Details</div>
            <div class="row">
                <span class="label">Plan:</span>
                <span class="value"><?= htmlspecialchars($payment['plan_name'] ?? 'N/A') ?></span>
            </div>
            <div class="row">
                <span class="label">Duration:</span>
                <span class="value"><?= $payment['duration_months'] ?? 0 ?> month(s)</span>
            </div>
            <div class="row">
                <span class="label">Method:</span>
                <span class="value"><?= ucfirst(htmlspecialchars($payment['payment_method'])) ?></span>
            </div>
            <div class="row">
                <span class="label">Date:</span>
                <span class="value"><?= date('M j, Y', strtotime($payment['payment_date'])) ?></span>
            </div>
        </div>
        
        <!-- Membership Period -->
        <div class="section">
            <div class="section-title">📅 Membership Period</div>
            <div class="row">
                <span class="label">Start:</span>
                <span class="value"><?= date('M j, Y', strtotime($payment['start_date'])) ?></span>
            </div>
            <div class="row">
                <span class="label">End:</span>
                <span class="value"><?= date('M j, Y', strtotime($payment['end_date'])) ?></span>
            </div>
        </div>
        
        <!-- Total Amount -->
        <div class="total">
            💰 TOTAL: <?= formatCurrency($payment['amount'], $currency) ?> 💰
        </div>
        
        <?php if ($payment['transaction_id'] || $payment['processed_by_name']): ?>
        <div class="section">
            <div class="section-title">ℹ️ Additional Info</div>
            <?php if ($payment['transaction_id']): ?>
            <div class="row">
                <span class="label">Transaction:</span>
                <span class="value"><?= htmlspecialchars($payment['transaction_id']) ?></span>
            </div>
            <?php endif; ?>
            <?php if ($payment['processed_by_name']): ?>
            <div class="row">
                <span class="label">Processed by:</span>
                <span class="value"><?= htmlspecialchars($payment['processed_by_name']) ?></span>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        

        
        <!-- Footer -->
        <div class="footer">
            <div class="thank-you">🎉 THANK YOU! 🎉</div>
            <div style="color: #ff00ff; font-weight: bold;">💪 Stay Strong! 💪</div>
            <div style="margin-top: 8px; font-size: 9px; color: #00ffff;">
                <?= date('M j, Y g:i A') ?>
            </div>
        </div>
        
        <!-- Print Buttons -->
        <div class="no-print">
            <button onclick="window.print()" class="print-btn">
                🖨️ Print Receipt
            </button>
            <button onclick="window.close()" class="close-btn">
                ❌ Close
            </button>
        </div>
    </div>
    
    <?php if ($autoPrint): ?>
    <script>
        // Auto-print when page loads
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 1000);
        };
    </script>
    <?php endif; ?>
</body>
</html>
