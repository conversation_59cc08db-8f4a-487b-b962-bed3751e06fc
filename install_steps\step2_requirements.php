<!-- Step 2: Requirements Check -->
<div class="text-center mb-6">
    <i class="fas fa-cogs text-4xl text-blue-600 mb-4"></i>
    <h2 class="text-2xl font-bold text-gray-900 mb-2">System Requirements</h2>
    <p class="text-gray-600">Checking your server environment</p>
</div>

<?php
// Check requirements
$requirements = [
    'PHP Version' => [
        'required' => '7.4.0',
        'current' => PHP_VERSION,
        'status' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'description' => 'PHP 7.4 or higher is required'
    ]
];

// Check extensions
$extensions = ['pdo', 'pdo_mysql', 'curl', 'json', 'mbstring', 'gd'];
foreach ($extensions as $ext) {
    $requirements["PHP Extension: {$ext}"] = [
        'required' => 'Enabled',
        'current' => extension_loaded($ext) ? 'Enabled' : 'Missing',
        'status' => extension_loaded($ext),
        'description' => "PHP {$ext} extension is required"
    ];
}

// Check directories
$directories = ['config', 'uploads', 'backups'];
foreach ($directories as $dir) {
    $exists = is_dir($dir);
    $writable = $exists && is_writable($dir);
    
    $requirements["Directory: {$dir}"] = [
        'required' => 'Writable',
        'current' => !$exists ? 'Missing' : ($writable ? 'Writable' : 'Not Writable'),
        'status' => $writable || (!$exists && is_writable('.')),
        'description' => "Directory {$dir} must be writable"
    ];
}

$allPassed = true;
foreach ($requirements as $req) {
    if (!$req['status']) {
        $allPassed = false;
        break;
    }
}
?>

<div class="space-y-4 mb-6">
    <?php foreach ($requirements as $name => $req): ?>
        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div class="flex items-center">
                <div class="w-6 h-6 rounded-full flex items-center justify-center mr-3
                    <?= $req['status'] ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600' ?>">
                    <i class="fas <?= $req['status'] ? 'fa-check' : 'fa-times' ?> text-sm"></i>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900"><?= htmlspecialchars($name) ?></h4>
                    <p class="text-sm text-gray-500"><?= htmlspecialchars($req['description']) ?></p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-sm font-medium <?= $req['status'] ? 'text-green-600' : 'text-red-600' ?>">
                    <?= htmlspecialchars($req['current']) ?>
                </p>
                <p class="text-xs text-gray-500">Required: <?= htmlspecialchars($req['required']) ?></p>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<?php if ($allPassed): ?>
    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
        <div class="flex">
            <i class="fas fa-check-circle text-green-400 mt-1 mr-3"></i>
            <div>
                <h4 class="text-green-800 font-medium">All Requirements Met!</h4>
                <p class="text-green-700">Your server environment is ready for MyGym installation.</p>
            </div>
        </div>
    </div>
    
    <form method="POST" class="space-y-6">
        <input type="hidden" name="step" value="2">
        
        <div class="flex justify-between">
            <a href="install_new.php?step=1" 
               class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center">
                <i class="fas fa-arrow-left mr-2"></i>
                Back
            </a>
            
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center">
                Continue
                <i class="fas fa-arrow-right ml-2"></i>
            </button>
        </div>
    </form>
    
<?php else: ?>
    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex">
            <i class="fas fa-exclamation-triangle text-red-400 mt-1 mr-3"></i>
            <div>
                <h4 class="text-red-800 font-medium">Requirements Not Met</h4>
                <p class="text-red-700">Please resolve the issues above before continuing with the installation.</p>
                <div class="mt-3">
                    <h5 class="font-medium text-red-800">Common Solutions:</h5>
                    <ul class="text-sm text-red-700 mt-1 list-disc list-inside">
                        <li>Contact your hosting provider to enable missing PHP extensions</li>
                        <li>Update PHP to version 7.4 or higher</li>
                        <li>Set directory permissions to 755 or 777 for required directories</li>
                        <li>Ensure your hosting plan supports the required features</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="flex justify-between">
        <a href="install_new.php?step=1" 
           class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center">
            <i class="fas fa-arrow-left mr-2"></i>
            Back
        </a>
        
        <button onclick="window.location.reload()" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center">
            <i class="fas fa-sync-alt mr-2"></i>
            Recheck Requirements
        </button>
    </div>
<?php endif; ?>
