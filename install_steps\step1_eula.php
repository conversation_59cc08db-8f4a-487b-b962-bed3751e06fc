<!-- Step 1: EULA Agreement -->
<div class="text-center mb-6">
    <i class="fas fa-file-contract text-4xl text-blue-600 mb-4"></i>
    <h2 class="text-2xl font-bold text-gray-900 mb-2">End User License Agreement</h2>
    <p class="text-gray-600">Please read and accept the license agreement to continue</p>
</div>

<div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6 max-h-96 overflow-y-auto">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">MyGym Management System - License Agreement</h3>
    
    <div class="text-sm text-gray-700 space-y-4">
        <p><strong>1. License Grant</strong></p>
        <p>This software is licensed, not sold. Subject to the terms of this Agreement, we grant you a non-exclusive, non-transferable license to use the MyGym Management System software.</p>
        
        <p><strong>2. Permitted Uses</strong></p>
        <ul class="list-disc list-inside ml-4 space-y-1">
            <li>Install and use the software on your server or hosting environment</li>
            <li>Customize the software for your business needs</li>
            <li>Create backups for your own use</li>
            <li>Use the software for commercial purposes</li>
        </ul>
        
        <p><strong>3. Restrictions</strong></p>
        <ul class="list-disc list-inside ml-4 space-y-1">
            <li>You may not redistribute, resell, or sublicense the software</li>
            <li>You may not remove copyright notices or proprietary markings</li>
            <li>You may not reverse engineer or decompile the software</li>
        </ul>
        
        <p><strong>4. Warranty Disclaimer</strong></p>
        <p>The software is provided "AS IS" without warranty of any kind. We disclaim all warranties, express or implied, including but not limited to merchantability and fitness for a particular purpose.</p>
        
        <p><strong>5. Limitation of Liability</strong></p>
        <p>In no event shall we be liable for any indirect, incidental, special, or consequential damages arising out of the use of this software.</p>
        
        <p><strong>6. Data Protection</strong></p>
        <p>You are responsible for complying with all applicable data protection laws and regulations when using this software to process personal data.</p>
        
        <p><strong>7. Support</strong></p>
        <p>This software is provided with installation support. Additional support may be available separately.</p>
        
        <p><strong>8. Updates</strong></p>
        <p>Updates and improvements may be provided at our discretion. You are not required to install updates, but they may contain important security fixes.</p>
        
        <p class="font-semibold">By clicking "I Agree" below, you acknowledge that you have read, understood, and agree to be bound by the terms of this license agreement.</p>
    </div>
</div>

<form method="POST" class="space-y-6">
    <input type="hidden" name="step" value="1">
    
    <div class="flex items-start">
        <div class="flex items-center h-5">
            <input id="agree_eula" 
                   name="agree_eula" 
                   type="checkbox" 
                   value="1"
                   class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                   required>
        </div>
        <div class="ml-3 text-sm">
            <label for="agree_eula" class="font-medium text-gray-700">
                I have read and agree to the End User License Agreement
            </label>
            <p class="text-gray-500">You must agree to the terms to continue with the installation.</p>
        </div>
    </div>
    
    <div class="flex justify-between">
        <div></div> <!-- Empty div for spacing -->
        
        <button type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center">
            I Agree & Continue
            <i class="fas fa-arrow-right ml-2"></i>
        </button>
    </div>
</form>

<script>
// Enable/disable submit button based on checkbox
document.getElementById('agree_eula').addEventListener('change', function() {
    const submitBtn = document.querySelector('button[type="submit"]');
    if (this.checked) {
        submitBtn.disabled = false;
        submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
    } else {
        submitBtn.disabled = true;
        submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
    }
});

// Initialize button state
document.addEventListener('DOMContentLoaded', function() {
    const checkbox = document.getElementById('agree_eula');
    const submitBtn = document.querySelector('button[type="submit"]');
    
    if (!checkbox.checked) {
        submitBtn.disabled = true;
        submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
    }
});
</script>
